<?php
// CEO 信箱 2.0版本 2020.11

namespace FlashExpress\bi\App\Enums;

use app\enums\LangEnums;

class CeoMailEnums extends BaseEnums
{

    const NEED_AGREE_CATEGORY_ID = [68];

    // CEO 信箱访问渠道
    const FEEDBACK_ACCESS_CHANNEL = 'feedback';
    const CEO_ACCESS_CHANNEL = 'ceo';
    const ACCESS_CHANNEL = [
        self::FEEDBACK_ACCESS_CHANNEL => 1,
        self::CEO_ACCESS_CHANNEL => 2,
    ];

    // CEO 问题工单评价常用语
    const COMMON_WORDS_001 = 'ceo_mail_evaluate_001';// 反馈及时
    const COMMON_WORDS_002 = 'ceo_mail_evaluate_002';// 解决问题
    const COMMON_WORDS_003 = 'ceo_mail_evaluate_003';// 礼貌热情
    const COMMON_WORDS_004 = 'ceo_mail_evaluate_004';// 功能棒
    const COMMON_WORDS_005 = 'ceo_mail_evaluate_005';// 反馈不及时
    const COMMON_WORDS_006 = 'ceo_mail_evaluate_006';// 态度差
    const EVALUATE_COMMON_WORDS_ZH = [
        self::COMMON_WORDS_001 => '反馈及时',
        self::COMMON_WORDS_002 => '解决问题',
        self::COMMON_WORDS_003 => '礼貌热情',
        self::COMMON_WORDS_004 => '功能棒',
        self::COMMON_WORDS_005 => '反馈不及时',
        self::COMMON_WORDS_006 => '态度差',
    ];

    const EVALUATE_COMMON_WORDS_EN = [
        self::COMMON_WORDS_001 => 'Timely feedback',
        self::COMMON_WORDS_002 => 'solve the problem',
        self::COMMON_WORDS_003 => 'Courtesy and enthusiasm',
        self::COMMON_WORDS_004 => 'Good function',
        self::COMMON_WORDS_005 => 'Feedback is not timely',
        self::COMMON_WORDS_006 => 'Poor attitude',
    ];

    const EVALUATE_COMMON_WORDS_TH = [
        self::COMMON_WORDS_001 => 'ตอบกลับรวดเร็ว',
        self::COMMON_WORDS_002 => 'ปัญหาได้รับการแก้ไข',
        self::COMMON_WORDS_003 => 'เอาใจใส่และตอบข้อซักถาม',
        self::COMMON_WORDS_004 => 'ฟังก์ชันดี',
        self::COMMON_WORDS_005 => 'ตอบกลับช้า',
        self::COMMON_WORDS_006 => 'คำตอบแย่',
    ];

    // 回复状态
    const HAVE_NO_REPLY_STATUS = 0;
    const HAVE_REPLIED_STATUS = 1;
    const HAVE_COMPLETED_STATUS = 2;
    const HAVE_TIMED_OUT_STATUS = 3;
    const HAVE_CLOSING_STATUS = 4;
    const HAVE_TIMED_OUT_SYS_STATUS = 5;//系统侧回复 超时状态。

    const REPLY_STATUE_ITEM = [
        self::HAVE_NO_REPLY_STATUS => 'ceo_mail_001',
        self::HAVE_REPLIED_STATUS => 'ceo_mail_002',
        self::HAVE_COMPLETED_STATUS => 'ceo_mail_003',
        self::HAVE_TIMED_OUT_STATUS => 'ceo_mail_004',
        self::HAVE_CLOSING_STATUS => 'ceo_mail_007',
    ];

    // 需要阅读信息承诺书的分类 = 针对大类(子类继承大类该属性) Information Commitment
    const ABOUT_COMPLAINT_CATEGORY_ID = 37; // 关于投诉
    const MUST_READ_INFORMATION_COMMITMENT_CATEGORY_IDS = [
        self::ABOUT_COMPLAINT_CATEGORY_ID
    ];


    public static function getCodeTxtMap($lang = '', string $code = '')
    {
        $txt = self::$code ?? '';
        return $txt;
    }

    const ABOUT_COMPENSATION_CATEGORY_ID = 3;//关于薪酬

    const ABOUT_CONTRACT_CATEGORY_ID = 192;//关于电子合同

    const ABOUT_INDEPENDENT_CONTRACT_CATEGORY_ID = 200;//关于个人代理电子合同

    const ABOUT_INDEPENDENT_VOUCHER_CATEGORY_ID = 220;//关于个人代理佣金

    const ABOUT_SYSTEM_CATEGORY = 18;//关于系统-其他

    const ABOUT_UPDATE_INFO = 224;//关于更改信息


    //薪酬分类
    const FLASH_BOX_CATEGORY = 'flash-box-feedback-list?source=salary';

    //flash box 分类页面
    const FLASH_BOX_URL      = 'flash-box-feedback?from=feedback';
    //invoice 进入的 flash box 分类
    const FLASH_BOX_CATEGORY_INVOICE = 'flash-box-feedback-list?source=invoice';

    //voucher 进入的 flash box 分类
    const FLASH_BOX_CATEGORY_VOUCHER = 'flash-box-feedback-list?source=voucher';

    //TH 电子合同分类
    const FLASH_BOX_CONTRACT_CATEGORY = 'flash-box-feedback-list?source=contract';

    //TH
    const FLASH_BOX_UPDATE_INFO_CATEGORY = 'flash-box-feedback-list?source=update_info';

    /**
     * flash-box-increase flash box 提交 页面 拼接参数
     * ?source=fbi&is_read_commitment=0&problem_category=' . CeoMailEnums::ABOUT_SYSTEM_CATEGORY . '&seconProblemName=' . $name
     * source ： 来源，例如：bi|hcm|by（前端需要跳转返回使用，后端 不接收使用）
     * problem_category 问题小类
     * seconProblemName 问题小类 的 名称
     */
    const FLASH_BOX_SUBMIT_PAGE = 'flash-box-increase';

    const DISPLAY_YES = 1;//展示
    const DISPLAY_NO = 0;//不展示



}
