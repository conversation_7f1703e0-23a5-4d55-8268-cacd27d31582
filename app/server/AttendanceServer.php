<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: nick
 * Date: 2019/5/8
 * Time: 下午3:25
 */

namespace FlashExpress\bi\App\Server;


use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Enums\AiStateEnums;
use FlashExpress\bi\App\Enums\AttendanceCalendarEnums;
use FlashExpress\bi\App\Enums\AttendanceEnums;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\Enums\CommonEnums;
use FlashExpress\bi\App\Enums\EnumSingleton;
use FlashExpress\bi\App\Enums\JobTransferEnums;
use FlashExpress\bi\App\Enums\MessageEnums;
use FlashExpress\bi\App\Enums\RedisEnums;
use FlashExpress\bi\App\Enums\SettingEnvEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateTime;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\HttpCurl;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\AttendanceLocationSettingModel;
use FlashExpress\bi\App\Models\backyard\AttendanceTemporaryCoordinateModel;
use FlashExpress\bi\App\Models\backyard\AttendanceTemporaryCoordinateStaffModel;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\BusinessTripModel;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrShiftModel;
use FlashExpress\bi\App\Models\backyard\HrShiftV2ExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffApplySupportStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftHistoryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftModel;
use FlashExpress\bi\App\Models\backyard\HrStaffShiftPresetModel;
use FlashExpress\bi\App\Models\backyard\MessagePdfModel;
use FlashExpress\bi\App\Models\backyard\MessageWarningModel;
use FlashExpress\bi\App\Models\backyard\OsStaffContractSignRecordModel;
use FlashExpress\bi\App\Models\backyard\OsStaffInfoExtendModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditModel;
use FlashExpress\bi\App\Models\backyard\StaffAuditReissueForBusinessModel;
use FlashExpress\bi\App\Models\backyard\StaffFaceBlacklistModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceAttachmentModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkAttendanceModel;
use FlashExpress\bi\App\Models\backyard\StaffWorkDetectFaceRecordModel;
use FlashExpress\bi\App\Models\backyard\AttendanceDataV2Model;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WorkHomeSettingModel;
use FlashExpress\bi\App\Models\backyard\WorkHomeStaffSettingModel;
use FlashExpress\bi\App\Models\coupon\MessageCourierModel;
use FlashExpress\bi\App\Modules\Id\Server\AttendanceAlertServer;
use FlashExpress\bi\App\Modules\Th\library\Enums\HrJobTitleEnums;
use FlashExpress\bi\App\Repository\AttendanceRepository;
use FlashExpress\bi\App\Repository\AuditRepository;
use FlashExpress\bi\App\Repository\BySettingRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use FlashExpress\bi\App\Repository\StaffDeviceInfoRepository;
use FlashExpress\bi\App\Repository\StaffOffDayRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceRepository;
use FlashExpress\bi\App\Server\Penalty\AttendancePenaltyServer;
use FlashExpress\bi\App\Server\Penalty\BasePenaltyServer;
use Ramsey\Uuid\Provider\Node\RandomNodeProvider;
use Ramsey\Uuid\Uuid;

class AttendanceServer extends BaseServer{


    public $timezone;
    //java 系统配置表 code
    //默认打卡范围 需求变更 该值更新为 网点默认打卡范围
//    protected $attendance_range = 'courier:app:backyard:attendance_range';//att_range_default
//    //总部坐标 courier:app:backyard:head_office:coordinates
//    protected $department_coordinates = 'courier:app:backyard:head_office:coordinates';//废弃
//    //网点临时坐标courier:app:backyard:store_temporary:coordinates
//    protected $other_coordinate = 'courier:app:backyard:store_temporary:coordinates';//store_temporary_coordinate
//    //logic 坐标courier:app:backyard:flash_logistic:coordinates
//    protected $logic_coordinates = 'courier:app:backyard:flash_logistic:coordinates';//logistic_coordinate
//    //fullment 坐标 courier:app:backyard:fulfillment:coordinates
//    protected $fullment_coordinates = 'courier:app:backyard:fulfillment:coordinates';//fulfillment_coordinate
//    //白名单员工 或者 职位是Branch Manager 、Branch Supervisor 可以相同网点类型 打外勤卡
//    protected $ignore_list = 'courier:app:backyard:field_punch_staff';//field_punch_staff
//    protected $connect_store = 'courier:app:backyard:store_temporary:store_id';//connect_store

    //从java数据库迁移到 by数据库取值
    //默认打卡范围 需求变更 该值更新为 网点默认打卡范围
    protected $attendance_range = 'att_range_default';
    //logic 坐标
    protected $logic_coordinates = 'logistic_coordinate';
    //fullment 坐标
    protected $fullment_coordinates = 'fulfillment_coordinate';
    //白名单员工 或者 职位是Branch Manager 、Branch Supervisor 可以相同网点类型 打外勤卡
    protected $ignore_list = 'field_punch_staff';
    //关联网点
    protected $connect_store = 'connect_store';

    //特殊职位配置 key
    protected $special_job_title = 'special_job_title';

    //网点员工 外勤白名单
    protected $store_punch_ignore_list = 'field_punch_store_staff';

    //需要上报里程的配置 职位
    protected $miles_job_title = 'miles_job_title';

    //总部员工 外勤总开关
    protected $department_switch = 'punch_department_switch';
    protected $department_punch_dep = 'punch_department_id';
    protected $department_punch_job = 'punch_department_job';
    protected $department_punch_list = 'punch_department_staffs';
    // 外勤配置开关key
    const FIELD_PUNCH_SWITCH_KEY = 'field_punch_switch';
    // 外勤配置开关对应值，1打开
    const FIELD_PUNCH_OPEN = 1;
    // 支援期间子账号功能开关key,0关闭,1打开
    const SUB_STAFF_SUPPORT_SWITCH_KEY = 'sub_staff_support_switch';
    // 支援期间子账号功能是否打开，0关闭,1打开
    const SUB_STAFF_SUPPORT_CLOSE = 0;
    const SUB_STAFF_SUPPORT_OPEN = 1;
    //代表是某部门所有职位
    const ALL_JOB_TITLE_OF_DEPARTMENT = 0;
    // 获取打卡地点配置开关key，0关闭，其他打开
    const ATTENDANCE_LOCATION_SWITCH_KEY = 'attendance_location_switch';
    // 外勤打卡数据，通知上级，发送消息+邮件 的队列
    const REDIS_FIELD_PERSONNEL_MESSAGE_LIST = 'field_personnel_message_list';

    protected $attendance_hour = 22;//距离上班时间22小时之内可以打下班卡
    protected $ignore_title = array(15);
    const COUNTRY_CODES = [
        '1' => 'THA',
        '2' => 'CHN',
        '3' => 'MYS',
        '4' => 'PHL',
        '5' => 'VNM',
        '6' => 'LAO',
        '99' => 'UKN', // 其他
    ];

    /**
     *
     * 工资银行代码映射
     */
    const BANK_TYPE_MAPS = [
        1 => '011',
        2 => '014',
    ];

    protected $score = '90';//人脸匹配 分数界定值

    const REDIS_SEARCH_STAFF_FACE_MESSAGE_KEY = 'search_staff_face_message_list';
    //通知客服系统 让对应工号的电话账号注销 只有下班打卡 调用
    const SYNC_CUSTOMER_KEY = 'sync_customer_attendance_card';
    //病假消息 key
    const SICK_MESSAGE = 'send_sick_msg';
    const REDIS_ASYNC_TRANSFER_ATTENDANCE_ATTACHMENT = 'async_transfer_attendance_attachment';
    //V21322【TH|BY|消息】 外协仓管自动发送合同 消息key
    const REDIS_SEND_OS_STAFF_CONTRACT_KEY = 'os_staff_contract';

    public $validate_face_with_mask = false;//默认 带口罩3次 提示

    //考勤打卡用到班次的地方
    public $shiftInfo;
    //是否是客服
    public $isCustomer;
    public $isSupport;//是否支援 当前登陆的是子账号
    public $isMasterSupport;//是否支援 登陆的是主账号
    public $masterStaffInfo;//当前登陆是子账号 查询主账号的信息
    public $supportInfo;//支援表记录


    public function __construct($lang = 'zh-CN',$timezone)
    {
        $this->timezone = $timezone;
        parent::__construct($lang);
    }
    //弹性班次职位
    protected $free_shift_position            = [];
    //弹性班次白班时长
    protected $free_shift_day_shift_duration  = 9;
    //弹性班次夜班时长
    protected $free_shift_night_shift_duration = 6;

    public function initFreeShiftConfig(): array
    {
        $settingConfig                         = (new SettingEnvServer())->getMultiEnvByCode([
            'day_shift_duration',
            'night_shift_duration',
            'free_shift_position',
        ]);
        $this->free_shift_position             = $settingConfig['free_shift_position'] ? explode(',',
            $settingConfig['free_shift_position']) : [];
        $this->free_shift_day_shift_duration   = $settingConfig['day_shift_duration'] ?? 9;
        $this->free_shift_night_shift_duration = $settingConfig['night_shift_duration'] ?? 6;
        return ['free_shift_position'             => $this->free_shift_position,
                'free_shift_day_shift_duration'   => $this->free_shift_day_shift_duration,
                'free_shift_night_shift_duration' => $this->free_shift_night_shift_duration,
        ];
    }



    /**
     * 申请加班ot时候 获取当天的考勤 --todo
     * @param $staff_id
     * @param $date
     */
    public function getAttendanceByDate($staff_id,$date)
    {

        $re = new AttendanceRepository($this->lang, $this->timezone);
        $info = $re->getDateInfo($staff_id,$date);

        if(!empty($info)){
            $start_stamp = strtotime($info['started_at']);
            $end_stamp = strtotime($info['end_at']);
            $info['duration'] = 0;
            if(!empty($info['started_at']) && !empty($info['end_at'])){
                $info['duration'] = floor(($end_stamp - $start_stamp)/3600);
            }
        }
        return $info;
    }


    //获取考勤信息

    /**
     *{
    "code": 1,
    "message": "success",
    "data": {
    "attendance_date": "2020-07-07",	（注：后面的接口传的打卡时间来自这个字段）
    "started_at": null,
    "click_in": 未打卡 1 已打卡 2 却卡 3
    "end_at": null,
    "click_after": 1,
    "attendance_range": "50000", 表 staff_work_attendance_range  总部 和没有配置的 默认都是200米
    "store_id": "TH05050601",
    "store_lat": 4.03034053,
    "store_lng": 16.41045049,
    "mileage_record_enabled": true, 里程汇报
    "more_staff_info_enabled": false, 一小时内 设备是否打卡过
    "original_url": true 是否配置了底片
     * 新增 字段 当前员工是否再出差
     *
    }
    }
     *
     * @param $param
     */
    public function attendance_info($param)
    {
        //弹性班次配置
        $this->initFreeShiftConfig();
        $return = array();
        $client = $param['client_id']??'';
        $user_info = $param['user_info'];
        $staff_id = $user_info['id'];
        $lat = empty($param['lat']) ? '' : $param['lat'];
        $lng = empty($param['lng']) ? '' : $param['lng'];

        $staff_re = new StaffRepository($this->lang);

        $staff_info = $staff_re->getStaffpositionV2($staff_id);
        if($staff_info['organization_type'] == 1){
            $store_info = SysStoreModel::findFirst("id = '{$staff_info['organization_id']}'");
            $store_info = is_object($store_info) ? $store_info->toArray() : $store_info;
            if (!empty($store_info)) {
                $user_info['store_category'] = $store_info['category'];
            }
            $staff_info['store_info'] = $store_info;
        }
        //是否是子账号
        $return['is_sub_staff'] = $staff_info['is_sub_staff'];
        //是否支援 和支援网点名称
        $return['is_support']    = false;
        $return['support_store'] = null;
        //雇佣类型
        $return['hire_type'] = $staff_info['hire_type'];
        //非个人代理
        //打卡页面 右上角的问题反馈地址
        $return['flash_box_url'] = isCountry(['TH', 'PH', 'ID']) && intval($staff_info['hire_type']) == HrStaffInfoModel::HIRE_TYPE_UN_PAID ? '' : env('h5_endpoint') . CeoMailEnums::FLASH_BOX_URL;

        $setting_model = new BySettingRepository($this->lang);
        $att_re = new AttendanceRepository($this->lang, $this->timezone);
        //获取配置 考勤范围 统一值 如果网点单独配置 下面获取坐标地方会覆盖
        $range = $setting_model->get_setting($this->attendance_range);
        //默认 200米
        if(empty($range)){
            $range = 200;
        }

        $return['mileage_record_enabled'] = false;
        // 职位是Van courier(110) car courier(1199) 并且是正式员工 打卡需要里程汇报
        $miles_job = $setting_model->get_setting($this->miles_job_title);
        if(!empty($miles_job) && in_array($staff_info['job_title'] , explode(',',$miles_job)) && $staff_info['formal'] == 1){
            $return['mileage_record_enabled'] = true;
        }
        //是否外勤字段 从device info 迁移过来
        $field_punch = $this->get_punch_field($staff_info);
        //是否子账号支援
        $this->isSupport = false;
        //根据客户端所在位置坐标 获取最近的打卡坐标
        $supportStaffInfo = [];
        if(!empty($lat) && !empty($lng)){

            $coordinate = array();//最终返回的最适合的坐标信息
            if($staff_info['organization_type'] == 1){

                $isOpen = (new SettingEnvServer())->getSetVal(AttendanceServer::SUB_STAFF_SUPPORT_SWITCH_KEY);
                if($isOpen == AttendanceServer::SUB_STAFF_SUPPORT_CLOSE){
                    //菲律宾支援 后面可能其他国家也要支援
                    if((isCountry('TH') || isCountry('PH') || isCountry('MY')) && $supportStaffInfo = $att_re->getSupportOsStaffInfo($staff_id)) {
                        $this->isSupport = true;
                        $staff_info['organization_id'] = $supportStaffInfo['store_id'];
                    }
                } else {
                    if((isCountry('TH') || isCountry('PH') || isCountry('MY')) && $supportStaffInfo = $att_re->getSupportInfoBySubStaff($staff_id)){
                        $this->isSupport = true;
                    }
                }
                $this->supportInfo = $supportStaffInfo;

                $co_list = array();
                //根据员工所在网点 取临时坐标 找最近的 （就是 一个网点有多个坐标 看哪个跟当前客户端最近）
                $co_data = $this->getTempCoordinate($staff_info);
                if(!empty($co_data)){
                    //网点多个坐标 取最近的
                    $batch_store = $this->get_most_close($co_data,$lat,$lng);
                    $batch_store['store_id'] = $staff_info['organization_id'];
                    $co_list[] = $batch_store;
                }

                // SAI网点:TH01470302 可以在 BKN网点：TH01180102 考勤打卡
                /**
                 * java 代码逻辑
                 * 1获取当前用户 网点的父网点信息
                 * 2 并且获取配置网点信息 {"TH01470302":["TH01180102"],"TH02060110":["TH02020402"],"TH02030208":["TH02060110"]}
                 * 3 取出来这些网点 以及对应的坐标点 然后 根据当前客户端位置 匹配那个最近 返回去
                 */
                $allowed_stores[] = $staff_info['organization_id'];//把员工所属网点先加进去
                $store_model = new SysStoreServer($this->lang);
                if($staff_info['job_title'] == 110 && $staff_info['formal'] == 1){//这个职位的 采取获取 父网点 java 的代码逻辑 别问我
                    $store_info = $store_model->getStoreByid($staff_info['organization_id']);
                    if(!$this->isSupport && !empty($store_info['ancestry'])){
                        $arr = explode('/',$store_info['ancestry']);
                        $allowed_stores[] = end($arr);
                    }
                }

                //获取关联打卡网点配置信息
                $all_connect = $setting_model->get_setting($this->connect_store);
                if(!empty($all_connect)){
                    $all_connect = json_decode($all_connect,true);
                    if(!empty($all_connect[$staff_info['organization_id']]))
                        $allowed_stores = array_merge($allowed_stores,$all_connect[$staff_info['organization_id']]);
                }

                //获取这些网点的id 和 坐标点  并且根据当前客户端坐标 获取最近的 经纬度
                $correct = $store_model->get_batch_store($allowed_stores);
                if(!empty($correct)){
                    $correct =  array_column($correct,null,'id');
                    foreach ($correct as $co){
                        $row['store_id'] = $co['id'];
                        $row['lng'] = $co['lng'];
                        $row['lat'] = $co['lat'];
                        $co_list[] = $row;
                    }
                }
                //白名单员工 或者 职位是Branch Manager 、Branch Supervisor 可以相同网点类型 打外勤卡
                $ignore_list = $setting_model->get_setting($this->ignore_list);
                if((!empty($ignore_list) && in_array($staff_id,explode(',',$ignore_list))) || in_array($staff_info['job_title'],$this->ignore_title)){
                    $store_category = $this->get_store_location($lat,$lng,array(),$user_info['store_category']);
                    if(!empty($store_category))
                        $co_list[] = $store_category;
                }

            }else{//总部的 取配置坐标
                $co_str = '';
                // Fulfillment 15
                if($staff_info['organization_id'] == 15)
                    $co_str = $setting_model->get_setting($this->fullment_coordinates);
                //Flash Logistic 28
                else if($staff_info['organization_id'] == 28)
                    $co_str = $setting_model->get_setting($this->logic_coordinates);
                //总部
                else{
                    //新需求 总部坐标和打卡范围 从配置改为 headquarters_attendance_range 表
                    $header_list = $att_re->get_header_range();
                }

                $co_str = empty($co_str) ? array() : explode('-',$co_str);
                $co_list = array();
                if(!empty($co_str)){
                    foreach ($co_str as $co){
                        $arr = explode(',',$co);
                        $row['lng'] = $arr[0];
                        $row['lat'] = $arr[1];
                        $co_list[] = $row;
                    }
                }
                //总部的 多个坐标点 以及对应的 打卡范围
                if(!empty($header_list)){
                    foreach ($header_list as $v){
                        $row['lng'] = $v['lng'];
                        $row['lat'] = $v['lat'];
                        $row['range'] = $v['attendance_range'];
                        $co_list[] = $row;
                    }
                }
            }

            //新需求 如果没有外勤打卡 并且 职位在配置内 增加取管辖范围网点逻辑 https://l8bx01gcjr.feishu.cn/docs/doccnbBxClv9dGbrsQGX9jgVMdd#
            $special_job_setting = $setting_model->get_setting($this->special_job_title);
            if(!$field_punch){
                $special_stores = $this->get_special_store($staff_info,$special_job_setting);
                $co_list = empty($special_stores) ? $co_list : array_merge($co_list,$special_stores);
            }

            // 从打卡地点设置表获取
            $settingStoreIds = $this->getPunchInStoreIds($staff_id, $co_list);
            if($settingStoreIds){
                $co_list = array_merge($co_list, $settingStoreIds);
            }

            if (RUNTIME == 'tra') {
                $this->logger->write_log([
                    'co_list'       => $co_list,
                    'staff_info_id' => $staff_id,
                ], 'info');
            }

            //最后处理 那个坐标 离得近
            if (!empty($co_list)) {
                $coordinate = $this->get_most_close($co_list, $lat, $lng);
            }

            //获取打卡范围 网点的有配置距离 总部的 取默认 范围
            if(!empty($coordinate['store_id'])){
                $range_info = $att_re->get_store_range($coordinate['store_id']);//根据网点获取 网点配置范围 表staff_work_attendance_range
            }
            $range = empty($range_info) ? $range : $range_info;

            $return['store_lat'] = empty($coordinate['lat']) ? '' : $coordinate['lat'];
            $return['store_lng'] = empty($coordinate['lng']) ? '' : $coordinate['lng'];
            $return['store_id'] = empty($coordinate['store_id']) ? '' : $coordinate['store_id'];
            $range = empty($coordinate['range']) ? $range : $coordinate['range'];
        }

        $return['support_staff_info'] = null;
        //菲律宾新增字段 客户端弹窗用
        $return['is_mobile_dc'] = false;
        if(isCountry('PH') && $staff_info['job_title'] == enums::$job_title['mobile_dc']){
            $return['is_mobile_dc'] = true;
        }

        /**
         *  1 => 'STATION',//收派件网点
        2 => 'DISTRIBUTION_CENTER',//分拨中心
        3 => 'AGENT_STATION',// 第三方代理收派件网点
        4 => 'MARKET_PICKUP_STATION',//揽件网点(市场)
        5 => 'STATION_SHOP',//收派件网点(shop)
        6 => 'FRANCHISEE_SHOP',// 加盟商网点
        7 => 'UNIVERSITY_SHOP',// 大学门店
        8 => 'HUB',
         */
        // 网点员工 一小时之内 该设备是否打过卡 并且 网点类型 是这些
        // 1 2 4 5 7
        // StoreCategory.STATION, StoreCategory.DISTRIBUTION_CENTER,
        // StoreCategory.MARKET_PICKUP_STATION,
        // StoreCategory.STATION_SHOP,StoreCategory.UNIVERSITY_SHOP)
        $return['more_staff_info_enabled'] = false; //false:允许打卡   true:不允许打卡并弹出提示
        if(!empty($client) && $staff_info['organization_type'] == 1 && in_array($user_info['store_category'],array(1,2,4,5,7))){
            $device_re = new StaffDeviceInfoRepository($this->lang);
            $exist = $device_re->get_device_one_hour($client,$staff_id);
            $return['more_staff_info_enabled'] = $exist > 0 ? true : false;
        }
        if (in_array(RUNTIME,['dev','tra','training'])) {
            $return['more_staff_info_enabled'] = false; //false:允许打卡   true:不允许打卡并弹出提示
        }

        //获取 是否存在底片
        $return['original_url'] = false;
        $re                     = new AttendanceRepository($this->lang, $this->timezone);
        $load_photo             = $re->get_attendance_photo($staff_id);
        if ($load_photo) {
            $return['original_url'] = true;
        }


        //东7区时间
        $shiftServer = new HrShiftServer();
        $dateList[] = date('Y-m-d');
        $dateList[] = date('Y-m-d',strtotime('-1 day'));
        $dateList[] = date('Y-m-d',strtotime('+1 day'));
        //页面显示班次 字符串用
        $shift_str = null;
        //获取三天 班次信息
        $this->shiftInfo = $shiftServer->getShiftInfos($staff_id, $dateList);
        $date = $return['attendance_date'] = $this->check_shift_date($staff_id);
        $today = date('Y-m-d');

        //正式考勤表信息
        $info = $this->get_att_by_date($staff_id,$date);

        /**
         *   4. 打卡时间和考勤日期规则
        1. 上班打卡时间，在班次周期（几点到几点）内，按班次的上班时间所在日期记录考勤日期；
        2. 上班打卡时间，不在班次周期内，当前打卡时间+4小时，如果在班次时间内，按班次的上班时间所在日期记录考勤；否则按打卡时间所在的日期记考勤。（如：班次为00:00-9:00，上班打卡时间是7月1号的23:30，记录考勤日期为7月2号。）
         */

        if(empty($info) && $date >= $today){//当天或者后一天考勤无数据 取前一天的
            $yesterday = date('Y-m-d', strtotime("{$date} -1 day"));
            $info = $this->get_att_by_date($staff_id, $yesterday);
        }
        $bus_ser = new BusinesstripServer($this->lang,$this->timezone);
        $this->logger->write_log(['attendance_info'=>$info,'date'=>$date,'staff_info_id'=>$staff_id,'input'=>$param], 'info');

        //有上班 没下班 判断时间间隔 总部22小时内 网点 16小时内都可打下班卡
        $base_hour = $this->attendance_hour;
        if(empty($info)){//班次对应日期 没有信息
            //默认值
            $return['attendance_date'] = $date;
            //如果 判定日期是今天 但是昨天没打卡 看是否 在 昨天上班班次 +22 之内 如果在 返回昨天 https://flashexpress.feishu.cn/docx/doxcnRPbiSL001nlFryAChZsHmf
            if ($return['attendance_date'] == $today && !empty($yesterday)) {//这个条件下 info 必定是 昨天的info 还是个空
                $flagForYesterday          = $this->checkYesterdayLast($staff_id, $yesterday);
                $return['attendance_date'] = $flagForYesterday ? $yesterday : $return['attendance_date'];
            }

            //  - 如果不是休息日，则当前打卡时间记录到当前日期的前一天  $date 有可能是今天 也可能是昨天
            if ($return['attendance_date'] < $today) {//如果是昨天 并且没有打卡 就判断昨天是不是休息日
                $flagForRest = $this->checkRestDate($staff_id, $return['attendance_date']);

                if ($flagForRest)//如果是休息日，则当前打卡时间记录到当前日期
                {
                    $return['attendance_date'] = $today;
                }
                //时间覆盖之后 要重新取值
                $infoToday = $this->get_att_by_date($staff_id, $today);
            }


            $return['started_at']       = $infoToday['started_at'] ?? null;
            $return['end_at']           = $infoToday['end_at'] ?? null;
            $return['click_in']         = empty($infoToday['started_at']) ? 1 : 2;
            $return['click_after']      = empty($infoToday['end_at']) ? 1 : 2;
            $return['attendance_range'] = intval($range);
            $return['start_data']       = empty($infoToday['started_at']) ? null : $infoToday['start_data'];
            $return['end_data']         = empty($infoToday['end_at']) ? null : $infoToday['end_data'];

            $return['is_on_business'] = $bus_ser->check_info_by_date($staff_id, $return['attendance_date']);
            $return['is_go_out']      = $bus_ser->check_go_out_by_date($staff_id, $return['attendance_date']);
            $return['field_punch']   = $field_punch;

            $this->checkSupportFlag($staff_id, $return['attendance_date']);
            //处理班次信息
            if(empty($this->masterStaffInfo)){
                [$return['shift_info'],$return['leave_tips']] = $this->makeLeaveTipsAndShiftInfo($staff_id,$staff_info['job_title'],$return['hire_type'],$return['attendance_date'],$return['start_data'],$this->shiftInfo[$return['attendance_date']]['start']??'',$this->shiftInfo[$return['attendance_date']]['end']??'');
            }else{
                //当前登陆是子账号 要查询主账号的 休息日 和请假
                [$return['shift_info'],$return['leave_tips']] = $this->makeLeaveTipsAndShiftInfo($this->masterStaffInfo['staff_info_id'],$this->masterStaffInfo['job_title'],$return['hire_type'],$return['attendance_date'],$return['start_data'],$this->shiftInfo[$return['attendance_date']]['start']??'',$this->shiftInfo[$return['attendance_date']]['end']??'');
            }
            $return['is_support'] = $this->isSupport || $this->isMasterSupport;
            $return['support_store'] = $this->supportInfo['store_name'] ?? null;
            return $return;
        }

        $start_stamp = empty($info['started_at']) ? 0 : strtotime($info['started_at']);


        //获取 班次班次 与 $start_stamp 取哪个早
        $start_stamp = $info['shift_start'] ? min(strtotime($info['date_at'] . ' ' . $info['shift_start']),$start_stamp) : $start_stamp;

        $current_tmp = time();
        if(empty($yesterday)){//班次对应日（有可能是 昨天 今天或者明天） 有数据
            //有上班没下班
            if(!empty($info['started_at']) && empty($info['end_at'])){
                $hour = ($current_tmp - $start_stamp) / 3600;
                $return['attendance_date'] = $date;
                $return['started_at'] = $info['started_at'];
                $return['click_in'] = 2;
                $return['end_at'] = null;
                $return['click_after'] = $hour > $base_hour ? 3 : 1;//1:可以打卡  3:缺卡，不能打卡
            }else if(!empty($info['started_at']) && !empty($info['end_at'])){//如果 班次对应日 都有数据
                if($date < $today){//都有数据了 返回当天
                    $info = $this->get_att_by_date($staff_id,$today);
                    $return['attendance_date'] = $today;
                    $return['started_at'] = empty($info['started_at']) ? null : $info['started_at'];
                    $return['click_in'] = empty($info['started_at']) ? 1 : 2;
                    $return['end_at'] = empty($info['end_at']) ? null : $info['end_at'];
                    $return['click_after'] = empty($info['end_at']) ? 1 : 2;
                }else{//今天 明天
                    $return['attendance_date'] = $date;
                    $return['started_at'] = $info['started_at'];
                    $return['click_in'] = 2;
                    $return['end_at'] = $info['end_at'];
                    $return['click_after'] = 2;
                }
            }else{//有下班卡了
                if($date < $today){//昨天有下班卡 返回今天
                    $info = $this->get_att_by_date($staff_id,$today);
                    $return['attendance_date'] = $today;
                    $return['started_at'] = empty($info['started_at']) ? null : $info['started_at'];
                    $return['click_in'] = empty($info['started_at']) ? 1 : 2;
                    $return['end_at'] = empty($info['end_at']) ? null : $info['end_at'];
                    $return['click_after'] = empty($info['end_at']) ? 1 : 2;
                }else{//今天 明天
                    $return['attendance_date'] = $date;
                    $return['started_at'] = null;
                    $return['click_in'] = 3;
                    $return['end_at'] = $info['end_at'];
                    $return['click_after'] = 2;
                }
            }

        }else{//date明日没数据 info 是今日  || date今日没数据 info是昨日的
            if(!empty($info['started_at']) && empty($info['end_at'])){//前一天没下班卡
                $hour = ($current_tmp - $start_stamp) / 3600;
                if($hour > $base_hour){//已经超过时间 昨天的打卡不算了
                    $return['attendance_date'] = $date;
                    $return['started_at'] = $return['end_at'] = null;
                    $return['click_in'] = $return['click_after'] = 1;

                }else{
                    $return['attendance_date'] = $yesterday;
                    $return['started_at'] = $info['started_at'];
                    $return['click_in'] = 2;
                    $return['end_at'] = null;
                    $return['click_after'] = 1;//判断 缺卡 3 还是 可以打卡 还没打 1
                }

            }else{
                $return['started_at'] = $return['end_at'] = null;
                $return['click_in'] = $return['click_after'] = 1;
            }
        }

        $return['start_data'] = empty($return['started_at']) ? null : $info['start_data'];
        $return['end_data'] = empty($return['end_at']) ? null : $info['end_data'];
        $return['attendance_range'] = intval($range);

        //是否存在出差记录
        $return['is_on_business'] = $bus_ser->check_info_by_date($staff_id,$return['attendance_date']);
        //是否存在外出记录
        $return['is_go_out'] = $bus_ser->check_go_out_by_date($staff_id,$return['attendance_date']);
        //车况检测 java 目前没用
        $return['fleet_driver_enabled'] = false;

        //员工当时在职状态
        $return['state'] = $staff_info['state'];
        $return['field_punch'] = $field_punch;

        $this->checkSupportFlag($staff_id, $return['attendance_date']);
        //处理班次信息
        if (empty($this->masterStaffInfo)) {
            [$return['shift_info'], $return['leave_tips']] = $this->makeLeaveTipsAndShiftInfo($staff_id,
                $staff_info['job_title'], $return['hire_type'], $return['attendance_date'], $return['start_data'],
                $this->shiftInfo[$return['attendance_date']]['start'] ?? '',
                $this->shiftInfo[$return['attendance_date']]['end'] ?? '');
        } else {
            //当前登陆是子账号 要查询主账号的 休息日 和请假
            [
                $return['shift_info'],
                $return['leave_tips'],
            ] = $this->makeLeaveTipsAndShiftInfo($this->masterStaffInfo['staff_info_id'],
                $this->masterStaffInfo['job_title'], $return['hire_type'], $return['attendance_date'],
                $return['start_data'], $this->shiftInfo[$return['attendance_date']]['start'] ?? '',
                $this->shiftInfo[$return['attendance_date']]['end'] ?? '');
        }
        $return['is_support'] = $this->isSupport || $this->isMasterSupport;
        $return['support_store'] = $this->supportInfo['store_name'] ?? null;

        return $return;
    }

    //新版获取临时坐标 的规则 https://flashexpress.feishu.cn/docx/FC0Ydq2ZxoT7rQxZx18cLmaun0b
    public function getTempCoordinate($staff_info)
    {
        $storeId = $staff_info['organization_id'];
        //获取员工详细详情
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('m.lat,m.lng');
        $builder->from(['m' => AttendanceTemporaryCoordinateModel::class]);
        $builder->leftjoin(AttendanceTemporaryCoordinateStaffModel::class, 'm.id = s.coordinate_id', 's');
        $builder->andWhere('m.is_delete = :is_delete:', ['is_delete' => AttendanceTemporaryCoordinateModel::UN_DELETE]);
        $builder->andWhere('m.store_id = :store_id:', ['store_id' => $storeId]);
        $builder->inWhere('s.staff_info_id',
            [$staff_info['staff_info_id'], AttendanceTemporaryCoordinateStaffModel::ALL_STAFF]);
        $data = $builder->getQuery()->execute()->toArray();
        return $data;
    }

    /**
     * 获取打卡地点配置
     * @param $staffInfoId
     * @param $co_list
     * @return array
     */
    public function getPunchInStoreIds($staffInfoId, $co_list)
    {
        $storeList = [];
        $settingModel = new BySettingRepository($this->lang);
        $switch = $settingModel->get_setting(self::ATTENDANCE_LOCATION_SWITCH_KEY);
        if($switch == 0) {
            return [];
        }

        $settingRes = AttendanceLocationSettingModel::findFirst(['conditions' => 'staff_info_id = :id:', 'bind' => ['id' => $staffInfoId]]);
        $settingRes = $settingRes ? $settingRes->toArray() : [];
        $settingStoreIds = ! empty($settingRes['store_data']) ? explode(',', $settingRes['store_data']) : [];
        if(empty($settingStoreIds)){
            return [];
        }

        $canAttendanceStoreIds = [];
        if(! empty($co_list)){
            foreach($co_list as $val){
                if(! empty($val['store_id'])) {
                    $canAttendanceStoreIds[] = $val['store_id'];
                }
            }
        }
        $searchStoreIds = [];
        // 是否包含总部:true是，false否
        $isHeaderOffice = false;
        foreach($settingStoreIds as $storeId){
            if(in_array($storeId, $canAttendanceStoreIds)){
                continue;
            }
            if($storeId == -1) {
                $isHeaderOffice = true;
                continue;
            }
            $searchStoreIds[] = $storeId;
        }


        if($searchStoreIds){
            $store_model = new SysStoreServer($this->lang);
            $correct = $store_model->get_batch_store($searchStoreIds);

            $correct =  array_column($correct,null,'id');
            foreach ($correct as $co){
                $row = [];
                $row['store_id'] = $co['id'];
                $row['lng'] = $co['lng'];
                $row['lat'] = $co['lat'];
                $storeList[] = $row;
            }
        }
        if($isHeaderOffice) {
            $att_re = new AttendanceRepository($this->lang, $this->timezone);
            $header_list = $att_re->get_header_range();
            //总部的 多个坐标点 以及对应的 打卡范围
            if(!empty($header_list)){
                foreach($header_list as $v){
                    $row = [];
                    $row['lng'] = $v['lng'];
                    $row['lat'] = $v['lat'];
                    $row['range'] = $v['attendance_range'];
                    $storeList[] = $row;
                }
            }
        }
        $this->logger->write_log("getPunchInStoreIds storeList=". json_encode($storeList), 'info');
        return $storeList;
    }

    /**
     * 马来-跨网点支援打卡优化,针对支援第一天上班和最后一天下班不做打卡地点校验
     * https://flashexpress.feishu.cn/docx/doxcng6a13Hjbp61m9Z6amulS3b
     * @param $attendance_category -打卡类型：上班卡，下班卡
     * @param $supportStaffInfo -支援网点信息
     * @param $attendanceDate -考勤日期
     * @param $shiftInfo 班次信息 只有近3天的 昨天今天明天
     * @return bool
     */
    public function boolFieldPunchForSupportStaff($attendance_category, $supportStaffInfo,$attendanceDate,$shiftInfo=[]): bool
    {
        if(! in_array($attendance_category, [
                AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD,
                AttendanceCalendarEnums::GET_OFF_WORK_MAKE_UP_CARD,
                AttendanceCalendarEnums::SECOND_GO_WORK_MAKE_UP_CARD,
                AttendanceCalendarEnums::SECOND_GET_OFF_WORK_MAKE_UP_CARD,
            ])
            || empty($supportStaffInfo)){
            return false;
        }

        //新逻辑 判断班次 是一次还是多次 决定 打卡类型是 2 还是4
        $endSide = AttendanceCalendarEnums::GET_OFF_WORK_MAKE_UP_CARD;
        //$shiftInfo[$thisDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_ONCE
        if(!empty($shiftInfo[$attendanceDate]) && $shiftInfo[$attendanceDate]['shift_type'] == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            $endSide = AttendanceCalendarEnums::SECOND_GET_OFF_WORK_MAKE_UP_CARD;
        }

        //支援期间第一天上班打卡
        $is_start_work = $attendance_category == AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD && ($attendanceDate == date('Y-m-d', strtotime($supportStaffInfo['employment_begin_date'])));
        //支援最后一天下班打卡
        $is_end_work = $attendance_category == $endSide && ($attendanceDate == date('Y-m-d', strtotime($supportStaffInfo['employment_end_date'])));

        if($supportStaffInfo['staff_store_id'] == enums::HEAD_OFFICE_ID) {
            return $is_start_work || $is_end_work;
        }

        //获取支援网点信息
        $support_store = (new SysStoreServer())->getStoreRegionPieceFromCache($supportStaffInfo['store_id'])?:[];
        //获取原网点信息
        $origin_store = (new SysStoreServer())->getStoreRegionPieceFromCache($supportStaffInfo['staff_store_id'])?:[];
        if(!$support_store || !$origin_store){
            return  false;
        }

        //1 “支援网点”与“员工所属网点”不属于相同片区 支援期间第一天上班和支援最后一天下班的 开外勤
        if($origin_store['manage_piece'] != $support_store['manage_piece']){
            return $is_start_work || $is_end_work;
        }

        //获取指定片区Id配置，此配置目的是片区内的网点之间相互支援不校验打卡。
        $setVal = (new SettingEnvServer())->getSetVal(SettingEnvEnums::NO_CHECK_ATTENDANCE_PIECE_CONFIG);
        $specify_piece_ids = empty($setVal) ? [] : explode(',', $setVal);

        //2 是否为 指定片区内的网点之间相互支援     是，支援期间第一天上班和支援最后一天下班的打卡 开外勤
        if(in_array($support_store['manage_piece'], $specify_piece_ids)){
            return $is_start_work || $is_end_work;
        }

        return false;
    }


    //整合正式表和出差打卡表数据
    public function get_att_by_date($staff_id, $date)
    {
        //正式考勤表信息
        $re   = new AttendanceRepository($this->lang, $this->timezone);
        $info = $re->getDateInfo($staff_id, $date);
        //新逻辑 出差期间 打卡操作 加了一层中间表(需审批后 保存) staff_audit_reissue_for_business
        $re_server    = new AttendanceBusinessServer($this->lang, $this->timezone);
        $reissue_info = $re_server->find_by_date($staff_id, $date);
        if (!empty($reissue_info) && !in_array($reissue_info['status'], [1, 2])) {
            $reissue_info = [];
        }

        //如果 正式考勤和 出差考勤 都有数据 需要拼接 以正式为准 出差考勤 补位
        if (!empty($info) && !empty($reissue_info)) {
            if (empty($info['started_at'])) {
                $info['started_at'] = $reissue_info['start_thai'];
                $info['start_data'] = $reissue_info['start_time'];
            }
            if (empty($info['end_at'])) {
                $info['end_at']   = $reissue_info['end_thai'];
                $info['end_data'] = $reissue_info['end_time'];
            }
            $info['shift_start'] = $info['shift_start'] ?: $reissue_info['shift_start'];
            $info['shift_end']   = $info['shift_end'] ?: $reissue_info['shift_end'];
        }
        if (empty($info) && !empty($reissue_info)) {
            //出差打卡时间存的是零时区 started_at 需要转换 泰国时间
            $info['date_at']     = $reissue_info['date_at'];
            $info['started_at']  = $reissue_info['start_thai'];
            $info['end_at']      = $reissue_info['end_thai'];
            $info['start_data']  = $reissue_info['start_time'];
            $info['end_data']    = $reissue_info['end_time'];
            $info['shift_start'] = $reissue_info['shift_start'];
            $info['shift_end']   = $reissue_info['shift_end'];
        }
        return $info;
    }


    /**
     * bi工具 修改考勤信息
     * @param $param
     */
    public function edit_att($param)
    {
        $start_tmp = strtotime($param['start_time']);
        $end_tmp   = strtotime($param['end_time']);

        $started_at = gmdate("Y-m-d H:i:s", $start_tmp);
        $end_at     = gmdate("Y-m-d H:i:s", $end_tmp);


        $staff_id = intval($param['staff_info_id']);
        $date     = $param['attendance_date'];


        //开始时间 结束时间 间隔 不能超过22小时 开始 不能小于结束
        if ($end_tmp - $start_tmp > ($this->attendance_hour * 3600)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('repair_limit_16'));
        }

        if ($start_tmp >= $end_tmp) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1010'));
        }

        //上班时间 不能早于 前一天下班时间
        $yesterday = date('Y-m-d', strtotime($date) - 24 * 3600);
        $yes_info  = $this->getAttendanceByDate($staff_id, $yesterday);
        $yes_end   = $yes_info['end_at'] ?? '';
        if (!empty($yes_end) && ($start_tmp < strtotime($yes_end))) {
            return $this->checkReturn(-3, $this->getTranslation()->_('repair_yesterday'));
        }

        //下班时间不能晚于后一天的上班
        $tomorrow = date('Y-m-d', strtotime($date) + 24 * 3600);
        $to_info  = $this->getAttendanceByDate($staff_id, $tomorrow);
        $to_start = $to_info['started_at'] ?? '';
        if (!empty($to_start) && ($end_tmp > strtotime($to_start))) {
            return $this->checkReturn(-3, $this->getTranslation()->_('repair_tomorrow'));
        }

        //更新数据库
        try {
            $info = $this->getAttendanceByDate($staff_id, $date);
            if (empty($info)) {
                return $this->checkReturn(-3, 'no attendance info');
            }
            if ($info['id'] != $param['id']) {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }

            if (empty($info['started_at']) || empty($info['end_at'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('start or end time is null'));
            }


            //新需求 可以修改 对应打卡信息的 班次
            if (!empty($param['shift_id']) && $param['shift_id'] > 0) {//操作人没有修改班次 不需要查询对应的班次 也不需要修改数据
                $shift_info  = HrShiftModel::findFirst(intval($param['shift_id']));
                $shift_start = empty($shift_info) ? '' : $shift_info->start;
                $shift_end   = empty($shift_info) ? '' : $shift_info->end;

                $update['shift_start'] = trim($shift_start);
                $update['shift_end']   = trim($shift_end);
                //同时 修改 hr_staff_shift_history 表数据
                $history_shift = HrStaffShiftHistoryModel::findFirst("staff_info_id = {$staff_id} and shift_day = '{$date}'");
                if (!empty($history_shift)) {
                    $old                       = $history_shift->toArray();
                    $history_shift->start      = $update['shift_start'];
                    $history_shift->end        = $update['shift_end'];
                    $history_shift->shift_type = empty($shift_info) ? '' : $shift_info->type;
                    $history_shift->shift_id   = $shift_info->id;
                    $history_shift->update();
                    $this->getDI()->get('logger')->write_log("hcm_edit_shift_{$staff_id} " . json_encode($old) . json_encode($param),
                        'info');
                }
            }
            $update['started_at']     = $started_at;
            $update['end_at']         = $end_at;
            $update['started_state']  = 5;
            $update['started_remark'] = 'bi_system';
            $update['end_state']      = 5;
            $update['end_remark']     = 'bi_system';
            $att_model                = new AttendanceRepository($this->lang, $this->timezone);

            $flag = $att_model->updateInfo($info['id'], $update);

            $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);

            if (isCountry('PH')) {
                //HCM补卡
                $attendancePenaltyServer->push($staff_id, $date, BasePenaltyServer::SRC_MAKE_UP);
            }

            //记录日志
            $this->getDI()->get('logger')->write_log("fbi_edit_att_{$staff_id} " . json_encode($param) . json_encode($update),
                'info');
            if (!$flag) {
                return $this->checkReturn(-3, 'failed');
            }

            $cardData['staff_id']        = $staff_id;
            $cardData['attendance_date'] = $date;
            $cardData['shift_type']      = empty($info) ? StaffWorkAttendanceModel::SHIFT_TYPE_ONLY : $info['shift_type'];
            $cardData['started_at']      = $started_at;
            $cardData['end_at']          = $end_at;
            //向FBI 生产 补卡消息-处罚
            $attendancePenaltyServer->sendCardReplacementMsg($cardData);

            return $this->checkReturn(1);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("fbi_edit_att_{$staff_id} " . $e->getMessage());
            return $this->checkReturn(-3, $e->getMessage());
        }
    }



    /**
     * 获取员工 考勤存档照片
     * @param $staff_id
     * @param $flag 是否拼接 内网链接 配合ai
     * @return string
     */
    public function get_face_img($staff_id, $flag = 0)
    {
        $sql = "select work_attendance_bucket,work_attendance_path from staff_work_attendance_attachment where staff_info_id = '{$staff_id}' and deleted = 0 order by created_at desc";
        $data = $this->getDI()->get('db')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

        if (empty($data)) {
            return '';
        }

        //拼接图片 全路径
        $param['bucket'] = $data['work_attendance_bucket'];
        $param['path'] = $data['work_attendance_path'];

        return $this->format_oss($param, $flag);
    }

    //第一次 打卡 保存底片
    public function save_face_img($param)
    {
        $db = $this->getDI()->get('db');
        $db->begin();
        try{

            $flag = $db->insertAsDict('staff_work_attendance_attachment', $param);
            if(!$flag){
                $db->rollback();
                $this->getDI()->get('logger')->write_log("保存底片失败 ".json_encode($param),'info');
                return false;
            }
            $id = $db->lastInsertId();
            //除了插入id 其他所有 改成删除
            $up_sql = " update staff_work_attendance_attachment set deleted = 1 where staff_info_id = {$param['staff_info_id']} and id != {$id}";

            $flag = $db->execute($up_sql);
            if(!$flag){
                $db->rollback();
                $this->getDI()->get('logger')->write_log("更新删除其他底片失败 {$id} ".json_encode($param),'info');
                return false;
            }
            $db->commit();
            return boolval($flag);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("保存底片失败 ".json_encode($param) . $e->getMessage());
            $db->rollback();
            return false;
        }

    }
    //拼接 oss 全路径  入参 bucket  - fle-staging-asset-internal  path - workAttendanceSource/1585032834-af4adf5f41524c83b7e14ec5f94a434a.jpg
    public function format_oss($param, $flag = 0)
    {
        //拼接图片 全路径
        $env_bll = new BySettingRepository();
        if($flag) {//拼接内网地址 配合ai
            $point = $env_bll->get_settingFromCache('server_point_internal');
        } else {
            $point = $env_bll->get_settingFromCache('server_point');
        }
        if (empty($point)) {
            return '';
        }
        return 'https://' . $param['bucket'].$point . $param['path'];
    }


    /**
     * @deprecated
     * @param $staff_info_id
     * @param $date
     * @return array
     */
    public function getStaffShift($staff_info_id, $date): array
    {
        $shift_start = '09:00';
        $shift_end   = '18:00';
        $shift_id    = 0;
        if ($date > date('Y-m-d')) {
            $shift_data = HrStaffShiftPresetModel::findFirst("staff_info_id = {$staff_info_id} and effective_date = '{$date}'");
            if (!empty($shift_data)) {
                $shift_start = $shift_data->start;
                $shift_end   = $shift_data->end;
                $shift_id    = $shift_data->shift_id;
            }
        }
        //没有预设
        if (empty($shift_data)) {
            $shift_data = HrStaffShiftModel::findFirst("staff_info_id = {$staff_info_id}");
            if (!empty($shift_data)) {
                $shift_start = $shift_data->start;
                $shift_end   = $shift_data->end;
                $shift_id    = $shift_data->shift_id;
                if ($date < $shift_data->effective_date) {
                    $shift_start = $shift_data->last_start;
                    $shift_end   = $shift_data->last_end;
                    $shift_id    = $shift_data->last_shift_id;
                }
            }
        }
        return [$shift_start, $shift_end, intval($shift_id)];
    }

    //匹配成功 后

    /**
     * @description 保存打卡记录
     * @param array $param 打卡参数
     * 参数含义
     * int attendance_category    1上班；2下班
     * int attendance_state    1内勤；2外勤
     * string attendance_date    打卡日期,样例: "2023-01-28"
     * timestamp click_at       打卡时间
     * float click_lat          纬度
     * float click_lng            经度
     * string remark            备注 （如果是内勤，该参数为nil）
     * string os                系统, 样例: "ios"
     * string clientid          客户端设备ID
     * string click_url         打卡照片(部分)地址
     *
     * https://l8bx01gcjr.feishu.cn/docs/doccnbhEvKdNws5LnQhnM4uV1wI
     * 如果是外勤打卡 1. 1.0版本老挝员工外勤打卡时提交的打卡理由与外勤打卡记录将被发送至员工直线上级的企业邮箱中
     *
     * @return mixed
     * @throws BusinessException
     */
    public function save_attendance($param)
    {
        $staff_id = $param['user_info']['id'];
        $date = $param['attendance_date'];

        //外勤打卡 需要备注
        if($param['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD && empty($param['remark'])){
            return array('code' => 100324, 'message' => $this->getTranslation()->_('miss_args'), 'data' => null);
        }

        if ($param['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD) {
            $staff_re   = new StaffRepository($this->lang);
            $staff_info = $staff_re->getStaffpositionV2($staff_id);
            if (!$this->get_punch_field($staff_info)) {
                throw new BusinessException($this->getTranslation()->_('not_within_the_attendance_range'));
            }
        }

        $staff_model = new StaffRepository($this->lang);
        $bi_staff_info = $staff_model->getStaffPosition($staff_id);

        $this->isCustomer = $this->isBelongCustomer($bi_staff_info);

        //获取班次 需求变更 如果打未来日期的上班卡 需要取预设没生效的 班次信息
        $shiftServer = new HrShiftServer();
        $shiftInfo   = $shiftServer->getShiftInfos($staff_id,[$date]);
        $shift_start = $shiftInfo[$date]['start'] ?? '09:00';
        $shift_end   = $shiftInfo[$date]['end'] ?? '18:00';
        $shift_id    = $shiftInfo[$date]['shift_id'] ?? 0;
        //如果是主播 班次信息是空
        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        if(in_array($param['user_info']['job_title'],$liveJobId)){
            $shift_start = $shift_end = '';
            $shift_id = 0;
        }

        $user_info = $param['user_info'];

        // 校验子账号打卡是否在支援期间内
        $masterStaffId = 0;
        $isOpen = (new SettingEnvServer())->getSetVal(AttendanceServer::SUB_STAFF_SUPPORT_SWITCH_KEY);
        $this->logger->write_log("sub_staff_support isOpen=$isOpen", 'info');

        if($isOpen == AttendanceServer::SUB_STAFF_SUPPORT_CLOSE){
            //去支援的员工 班次使用申请表的
            if($supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportOsStaffInfo($staff_id,$date)) {
                $log_info = "support staff shift and organization_id changed !!! organization_id before:" . $user_info['organization_id'] . ' after:' . $supportStaffInfo['store_id'];
                $log_info .= "shift info : before " . json_encode([$shift_start , $shift_end,$shift_id], JSON_UNESCAPED_UNICODE) . ' after:' . json_encode($supportStaffInfo, JSON_UNESCAPED_UNICODE);
                $this->logger->write_log($log_info, 'info');
//                $shift_start = $supportStaffInfo['shift_start'];
//                $shift_end = $supportStaffInfo['shift_end'];
//                $shift_id = $supportStaffInfo['shift_id'];

                $user_info['organization_id'] = $supportStaffInfo['store_id'];
            }
            $this->logger->write_log("sub_staff_support supportinfo=" .  json_encode($supportStaffInfo, JSON_UNESCAPED_UNICODE), 'info');
        } else {
            $isSubStaff = $bi_staff_info['is_sub_staff'] ?? 0;
            $isSubStaff = (isCountry('TH') || isCountry('PH') || isCountry('MY')) && $isSubStaff == 1;
            $supportStaffInfo = [];
            if($isSubStaff) {
                $supportStaffInfo = (new AttendanceRepository($this->lang,$this->timezone))->getSupportInfoBySubStaff($staff_id,$date);
                if(empty($supportStaffInfo['staff_info_id'])){
                    return array('code' => 100337, 'message' => $this->getTranslation()->_('staff_support_expired'), 'data' => null);
                }
                $masterStaffId = $supportStaffInfo['staff_info_id'];
//                $shift_start = $supportStaffInfo['shift_start'];
//                $shift_end = $supportStaffInfo['shift_end'];
//                $shift_id = $supportStaffInfo['shift_id'];

                $log_info = 'shift info : before '.json_encode([$shift_start, $shift_end,$shift_id],
                        JSON_UNESCAPED_UNICODE).' after:'.json_encode($supportStaffInfo, JSON_UNESCAPED_UNICODE);
                $this->logger->write_log($log_info , 'info');
            }
            $this->logger->write_log("sub_staff_support isSubStaff=$isSubStaff, supportinfo=" .  json_encode($supportStaffInfo, JSON_UNESCAPED_UNICODE), 'info');

        }

        //打卡没有坐标
        if (empty($param['click_lat']) || empty($param['click_lng'])) {
            return ['code' => 100328, 'message' => $this->getTranslation()->_('wrong coordinate'), 'data' => null];
        }

        //重新验证 考勤日期 上下班类型 和 获取对应打卡网点坐标
        $p['client_id'] = $param['clientid'];
        $p['lat'] = $param['click_lat'];
        $p['lng'] = $param['click_lng'];
        $p['user_info'] = $param['user_info'];
        $p['attendance_category'] = $param['attendance_category'];
        $att_info = $this->attendance_info($p);

        //日期不对 100325
        if ($att_info['attendance_date'] != $param['attendance_date']) {
            return ['code' => 100325, 'message' => $this->getTranslation()->_('wrong date'), 'data' => null];
        }

        //上下班类型不对  未打卡:1  已打卡:2  缺卡:3
        if ($param['attendance_category'] == 1 && $att_info['click_in'] != 1) {
            return ['code' => 100336, 'message' => $this->getTranslation()->_('wrong attendance type'), 'data' => null];
        }
        if ($param['attendance_category'] == 2 && $att_info['click_after'] != 1) {
            return ['code' => 100336, 'message' => $this->getTranslation()->_('wrong attendance type'), 'data' => null];
        }

        //是否是工作日
        $working_day = $staff_model->get_is_working_day($bi_staff_info,$date);

        //新需求 https://l8bx01gcjr.feishu.cn/docs/doccnZzrd3PvTSB7BDw2fCphGJf#
        //判断是否在出差状态 没有出差 走原来流程
        $business_re = new BusinesstripServer($this->lang,$this->timezone);
        $business_trip_data = $business_re->getExitTypeByDate($staff_id, $date);

        //存在出差状态 打卡操作 不写打卡表生成审批
        if($business_trip_data){//生成审批记录 但是 不保存大列表 每天 中午12点 跑一次  attendance_date 等于昨天的记录
            $param['shift_start'] = $shift_start;
            $param['shift_end'] = $shift_end;
            $param['shift_id'] = $shift_id;
            $param['staff_info_id'] = $staff_id;
            $param['working_day'] = $working_day;
            $param['business_trip_type'] = $business_trip_data['business_trip_type'];
            $param['destination_country'] = $business_trip_data['destination_country'];
            $this->logger->write_log("save_attendance_business {$staff_id} " . json_encode($param),'info');
            return $this->check_on_trip($param);
        }
        //判定 add 还是 更新
        $re = new AttendanceRepository($this->lang, $this->timezone);
        $info = $re->getDateInfo($staff_id,$date);

        //获取同设备 打卡次数
        $device_re = new StaffDeviceInfoRepository($this->lang);
        $client_num = $device_re->get_click_num($param['clientid'],$param['attendance_date'],$param['attendance_category']);
        $client_num = intval($client_num) + 1;

        $currentTime           = time();
        $insert['shift_start'] = $shift_start;
        $insert['shift_end']   = $shift_end;
        $insert['shift_id']    = $shift_id;
        $insert['working_day'] = $working_day;

        if(empty($info)){
            //组织 保存数据
            $insert['attendance_date'] = $param['attendance_date'];
            $insert['staff_info_id'] = $staff_id;
            $insert['organization_id'] = ($bi_staff_info['sys_store_id'] == '-1') ? $bi_staff_info['sys_department_id'] : $bi_staff_info['sys_store_id'];
            $insert['organization_type'] = $user_info['organization_type'];
            $insert['job_title'] = intval($bi_staff_info['job_title']);
            $insert['shift_start'] = $shift_start;
            $insert['shift_end'] = $shift_end;
            $insert['shift_id'] = $shift_id;
            $insert['working_day'] = $working_day;
            if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON){
                $insert['started_at'] = gmdate('Y-m-d H:i:s',$currentTime);
                $insert['started_state'] = intval($param['attendance_state']);
                $insert['started_staff_lat'] = $param['click_lat'];
                $insert['started_staff_lng'] = $param['click_lng'];
                $insert['started_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['started_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['started_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['started_clientid'] = $param['clientid'];
                $insert['started_clientid_num'] = $client_num;
                $insert['started_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['started_os'] = $param['os'];
                $insert['started_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                //$bucket =  $this->getDI()['config']['application']['oss_bucket'];
                $insert['started_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['started_remark'] = empty($param['remark']) ? '' : $param['remark'];
            }else{//下班打卡
                $insert['end_at'] = gmdate('Y-m-d H:i:s',$currentTime);
                $insert['end_state'] = intval($param['attendance_state']);
                $insert['end_staff_lat'] = $param['click_lat'];
                $insert['end_staff_lng'] = $param['click_lng'];
                $insert['end_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['end_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['end_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['end_clientid'] = $param['clientid'];
                $insert['end_clientid_num'] = $client_num;
                $insert['end_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['end_os'] = $param['os'];
                $insert['end_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                //$bucket =  $this->getDI()['config']['application']['oss_bucket'];
                $insert['end_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['end_remark'] = empty($param['remark']) ? '' : $param['remark'];
            }
        }else{
            if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON){
                $insert['started_at'] = gmdate('Y-m-d H:i:s',$currentTime);
                $insert['started_state'] = intval($param['attendance_state']);
                $insert['started_staff_lat'] = $param['click_lat'];
                $insert['started_staff_lng'] = $param['click_lng'];

                $insert['started_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['started_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['started_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['started_clientid'] = $param['clientid'];
                $insert['started_clientid_num'] = $client_num;
                $insert['started_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['started_os'] = $param['os'];
                $insert['started_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                $insert['started_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['started_remark'] = empty($param['remark']) ? '' : $param['remark'];
            }else{//下班打卡
                $insert['end_at'] = gmdate('Y-m-d H:i:s',$currentTime);
                $insert['end_state'] = intval($param['attendance_state']);
                $insert['end_staff_lat'] = $param['click_lat'];
                $insert['end_staff_lng'] = $param['click_lng'];

                $insert['end_store_id'] = empty($att_info['store_id']) ? null : $att_info['store_id'];
                $insert['end_store_lng'] = empty($att_info['store_lng']) ? null : $att_info['store_lng'];
                $insert['end_store_lat'] = empty($att_info['store_lat']) ? null : $att_info['store_lat'];
                $insert['end_clientid'] = $param['clientid'];
                $insert['end_clientid_num'] = $client_num;
                $insert['end_equipment_type'] = $param['equipment'] == 'KIT' ? 1 : 3;//KIT 1  BACKYARD 3
                $insert['end_os'] = $param['os'];
                $insert['end_path'] = $param['click_url'];//带模块的文件名  attxxx/aa.jpg
                $insert['end_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
                $insert['end_remark'] = empty($param['remark']) ? '' : $param['remark'];
            }
        }

        // 主账号打卡
        if($masterStaffId == 0){
            if(empty($info['id'])){
                $db = $this->getDI()->get('db');
                $flag = $db->insertAsDict('staff_work_attendance', $insert);
            } else {
                if (empty($info['shift_start'])) {
                    $insert['shift_start'] = $shift_start;
                    $insert['shift_end']   = $shift_end;
                    $insert['shift_id']    = $shift_id;
                    $insert['working_day'] = $working_day;
                }
                $att_model = new AttendanceRepository($this->lang, $this->timezone);
                $flag = $att_model->updateInfo($info['id'], $insert);
            }
            $this->logger->write_log("save_attendance_{$staff_id} {$flag} " . json_encode($insert),'info');

            //外勤打卡数据--发送给上级：消息+邮件
            if ($param['attendance_state'] == StaffWorkAttendanceModel::STATE_FIELD_PERSONNEL_CARD && $flag) {
                $data['staff_info_id']       = $param['user_info']['id'];//工号
                $data['name']                = $param['user_info']['name'];//姓名
                $data['clock_time']          = $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON ? $insert['started_at'] : $insert['end_at'];//零时区,打卡时间
                $data['attendance_category'] = $param['attendance_category'];//上/下班 卡
                $data['staff_lat']           = $param['click_lat'];//纬度
                $data['staff_lng']           = $param['click_lng'];//经度
                $data['attendance_date']     = $param['attendance_date'];//考勤日期
                $data['remark']              = $param['remark'];//外勤原因
                $this->sendFieldPersonnelMessage($data);
            }
        }else{
            // 子账号打卡
            if (empty($info['id'])) {
                $flag = $this->insertSubStaffAttendance($date, $insert, $masterStaffId, $shift_start, $shift_end , $shift_id);
            } else {
                if (empty($info['shift_start'])) {
                    $insert['shift_start'] = $shift_start;
                    $insert['shift_end']   = $shift_end;
                    $insert['shift_id']    = $shift_id;
                    $insert['working_day'] = $working_day;
                }
                $flag = $this->updateSubStaffAttendance(
                    $info['id'], $date, $insert, $masterStaffId, $shift_start, $shift_end , $shift_id);
            }
        }
        $this->logger->write_log("save_attendance_{$staff_id} {$flag} " . json_encode($insert),'info');
        //推送上下班打卡时间和坐标到MS
        $clock_data = [
            'staffInfoId' => intval($staff_id),
            'clockAt'     => $currentTime,
            'lat'         => $param['click_lat'],
            'lng'         => $param['click_lng'],
            'type'        => intval($param['attendance_category']),//1,上班，2下班
            'deviceId'    => $param['clientid'],
        ];
        $this->sendClockToMs($clock_data);

        //下班打卡通知客服系统 只有 泰国 马来 菲律宾 同步
        if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_OFF && $this->isCustomer && isCountry('TH')){
            $this->syncWebHookCustomer($bi_staff_info, $param);
        }

        //泰国上班打卡后逻辑处理
        if($param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_ON && isCountry('TH')) {
            //病假新规则 打卡发消息
            $this->sickMessage($bi_staff_info, $param);

            //V21322【TH|BY|消息】 外协仓管自动发送合同
            $this->sendOsStaffContract($bi_staff_info);
        }

        if (isCountry('TH') && $bi_staff_info['job_title'] == HrJobTitleEnums::JOB_TITLE_SENIOR_BRANCH_SUPERVISOR) {
            $this->syncAttendanceToVirtualStaffPush($staff_id,$date);
        }

        if (isCountry('TH') && in_array($bi_staff_info['hire_type'],HrStaffInfoModel::$agentTypeTogether) && $param['attendance_category'] == StaffWorkAttendanceModel::ATTENDANCE_OFF) {
            $sendStaffId = empty($masterStaffId) ? $staff_id : $masterStaffId;
            //如果支援 要传主账号
            $dateTime = date('Y-m-d H:i:s',$currentTime);//本地时间
            $this->sendIcOffCard($sendStaffId,$date, $dateTime);
        }
        //外协员工记录上班打卡时所在外协公司
        if (in_array($bi_staff_info['staff_type'], [1, 2, 3])
            && $bi_staff_info['sys_store_id'] != '-1') {
            $this->syncAttendanceForCompanyNameEfPush($staff_id, $date);
        }
        return $flag;
    }

    /**
     * 高级主管的考勤同步到虚拟账号
     * @param $staff_id
     * @param $attendance_date
     * @return void
     */
    protected function syncAttendanceToVirtualStaffPush($staff_id, $attendance_date)
    {
        $redis = $this->getDI()->get('redisLib');
        $data  = json_encode(['staff_id' => $staff_id, 'attendance_date' => $attendance_date]);
        $this->getDI()->get("logger")->write_log('syncAttendanceToVirtualStaff data  ' . $data, "info");
        return $redis->lpush(RedisEnums::LIST_SYNC_ATTENDANCE_TO_VIRTUAL_STAFF, $data);
    }

    /**
     * 高级主管的考勤同步到虚拟账号
     * @param $staff_id
     * @param $attendance_date
     * @return bool
     * @throws BusinessException
     */
    public function syncAttendanceToVirtualStaffPop($staff_id,$attendance_date): bool
    {
        $db = $this->getDI()->get('db');

        $sql                     = "select * from staff_work_attendance where staff_info_id =:staff_info_id and attendance_date =:attendance_date";
        $bind['staff_info_id']   = $staff_id;
        $bind['attendance_date'] = $attendance_date;
        $attendance_info          = $db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        if (empty($attendance_info)) {
            throw new BusinessException(json_encode(['staff_id'                        => $staff_id,
                                                     'attendance_data'                 => $attendance_date,
                                                     'syncAttendanceToVirtualStaffPop' => 'empty!!!',
            ], JSON_UNESCAPED_UNICODE));
        }
        //获取高级主管的虚拟工号
        $sql  = 'SELECT * FROM supervisor_store_info WHERE staff_id = :staff_id and deleted = 0';
        $virtual_staff_data = $this->getDI()->get('db_fle')->query($sql, ['staff_id'=>$staff_id])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if(empty($virtual_staff_data)){
            return true;
        }

        $db->begin();
        $virtual_staff_id = array_column($virtual_staff_data, 'virtual_staff_id');
        $del_sql = "delete from staff_work_attendance where staff_info_id in (".implode(',',$virtual_staff_id).") and attendance_date =:attendance_date";
        $db->execute($del_sql,['attendance_date'=>$attendance_date]);
        $insert_item = [];
        foreach ($virtual_staff_data as $virtual_staff_datum) {
            $insert_data = $attendance_info;
            $insert_data['id'] = 0;
            $insert_data['staff_info_id'] = $virtual_staff_datum['virtual_staff_id'];
            $insert_item[] = $insert_data;
        }
        (new StaffWorkAttendanceModel())->batch_insert($insert_item,BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME);
        $db->commit();
        return true;
    }
    /**
     * 外协上班打卡记录所在外协公司
     * @param $staff_id
     * @param $attendance_date
     * @return void
     */
    protected function syncAttendanceForCompanyNameEfPush($staff_id, $attendance_date)
    {
        $data = json_encode(['staff_info_id' => $staff_id, 'attendance_date' => $attendance_date]);
        $this->logger->write_log('syncAttendanceForCompanyNameEfPush data  ' . $data, "info");
        return $this->redisLib->lpush(RedisEnums::SYNC_ATTENDANCE_FOR_COMPANY_NAME_EF, $data);
    }

    /**
     * 外协上班打卡记录所在外协公司消费
     * @param $staff_id
     * @param $attendance_date
     * @return void
     */
    public function syncAttendanceForCompanyNameEfPop($staff_id, $attendance_date)
    {
        $sql                     = "select id,updated_at,end_at,company_name_ef from staff_work_attendance where staff_info_id =:staff_info_id and attendance_date =:attendance_date";
        $bind['staff_info_id']   = $staff_id;
        $bind['attendance_date'] = $attendance_date;
        $attendance_info         = $this->db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, $bind);
        //还未生成 打包送回队列
        if (empty($attendance_info)) {
            $data = json_encode(['staff_info_id' => $staff_id, 'attendance_date' => $attendance_date]);
            $this->logger->write_log('syncAttendanceForCompanyNameEfPush replace data  ' . $data, "info");
            return $this->redisLib->lpush(RedisEnums::SYNC_ATTENDANCE_FOR_COMPANY_NAME_EF, $data);
        }
        if (!empty($attendance_info['company_name_ef'])) {
            return;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("h.company_name_ef,os.company_item_id");
        $builder->from(['h' => HrStaffInfoModel::class]);
        $builder->leftJoin(OsStaffInfoExtendModel::class, 'h.staff_info_id = os.staff_info_id', 'os');
        $builder->where('h.staff_info_id=:staff_id:', ['staff_id' => $staff_id]);
        $staff                     = $builder->getQuery()->getSingleResult();
        $params['company_name_ef'] = empty($staff) ? '' : (isCountry() ? $staff->company_item_id : $staff->company_name_ef);
        $params['updated_at']      = $attendance_info['updated_at'];
        $this->db->updateAsDict('staff_work_attendance', $params, [
            "conditions" => " id = ? ",
            "bind"       => [$attendance_info['id']],
        ]);
    }
    /**
     * @description 新增子账号和主账号考勤信息
     * @param string $date 考勤日期
     * @param array $insert 待插入数据
     * @param int $masterStaffId 主账号ID
     * @param string $shift_start 班次开始时间
     * @param string $shift_end 班次结束时间
     * @param int $shift_id 班次ID
     * @return boolean
     */
    public function insertSubStaffAttendance($date, $insert, $masterStaffId, $shift_start, $shift_end , $shift_id=0)
    {
        $masterStaffInfo = HrStaffInfoModel::findFirst(['conditions' => ' staff_info_id = :staff_id: ', 'bind' => ['staff_id' => $masterStaffId]]);
        $masterStaffInfo = $masterStaffInfo ? $masterStaffInfo->toArray() : [];
        $sysStoreId = $masterStaffInfo['sys_store_id'];
        if(empty($sysStoreId)){
            $this->getDI()->get('logger')->write_log("新增子账号和主账号考勤信息-查询主账号信息异常", 'error');
            return false;
        }

        $re = new AttendanceRepository($this->lang, $this->timezone);
        $masterAttendance = $re->getDateInfo($masterStaffId, $date);

        $db = $this->getDI()->get('db');
        try {
            $db->begin();



            $flag = $db->insertAsDict('staff_work_attendance', $insert);

            if(empty($masterAttendance)){
                $masterInsert = $insert;
                $masterInsert['staff_info_id'] = $masterStaffId;
                $masterInsert['organization_id'] = $sysStoreId;
                $masterFlag = $db->insertAsDict('staff_work_attendance', $masterInsert);
                $this->logger->write_log("sub_staff_support_insert masterFlag=$masterFlag, subFlag=$flag, data=" .  json_encode($insert, JSON_UNESCAPED_UNICODE), 'info');
            } else {

                if((empty($masterInsert['started_at']) && !empty($insert['started_at'])) || (empty($masterInsert['end_at']) && !empty($insert['end_at']))){
                    $masterInsert = $insert;
                    if (!empty($insert['started_at']) && !empty($masterAttendance['start_data'])) {
                        $masterInsert['started_at'] = min($insert['started_at'], $masterAttendance['start_data']);
                    }
                    if (!empty($insert['end_at']) && !empty($masterAttendance['end_data'])) {
                        $masterInsert['end_at'] = max($insert['end_at'], $masterAttendance['end_data']);
                    }
                }
                // 如果已有打卡，则只更新班次
                $masterInsert['shift_start'] = $shift_start;
                $masterInsert['shift_end'] = $shift_end;
                $masterInsert['shift_id'] = $shift_id;
                $masterInsert['staff_info_id'] = $masterStaffId;
                $masterInsert['organization_id'] = $sysStoreId;
                $masterFlag = $db->updateAsDict('staff_work_attendance', $masterInsert, ['conditions' => "id=?", 'bind' => [$masterAttendance['id']]]);
                $this->logger->write_log("sub_staff_support_update masterFlag=$masterFlag, subFlag=$flag, data=" .  json_encode($insert, JSON_UNESCAPED_UNICODE), 'info');
            }
            $db->commit();
        } catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log("新增子账号和主账号考勤信息-写入失败异常信息:" . $e->getMessage(), 'error');
            $flag = false;
        }
        return $flag;
    }

    /**
     * 更新子账号和主账号考勤信息
     * @param $id
     * @param string $date 考勤日期
     * @param array $insert 待插入数据
     * @param int $masterStaffId 主账号ID
     * @param string $shift_start 班次开始时间
     * @param string $shift_end 班次结束时间
     * @param int $shift_id 班次ID
     * @return false
     */
    public function updateSubStaffAttendance($id, $date, $insert, $masterStaffId, $shift_start, $shift_end , $shift_id=0)
    {
        $re = new AttendanceRepository($this->lang, $this->timezone);
        $masterAttendance = $re->getDateInfo($masterStaffId, $date);
        if(empty($masterAttendance)) {
            $this->getDI()->get('logger')->write_log("更新子账号考勤信息-查询主账号考勤失败异常信息:masterStaffId=$masterStaffId, $date=$date", 'error');
            return false;
        }

        $db = $this->getDI()->get('db');
        try {
            $db->begin();
            // 更新子账号
            $flag = $db->updateAsDict('staff_work_attendance', $insert, ['conditions' => "id=?", 'bind' => [$id]]);

            // 更新主账号
            $masterInsert = $insert;

            if (!empty($insert['started_at']) && !empty($masterAttendance['start_data'])) {
                $masterInsert['started_at'] = min($insert['started_at'], $masterAttendance['start_data']);
            }
            if (!empty($insert['end_at']) && !empty($masterAttendance['end_data'])) {
                $masterInsert['end_at'] = max($insert['end_at'], $masterAttendance['end_data']);
            }
            $masterInsert['staff_info_id'] = $masterStaffId;
            $masterInsert['shift_start'] = $shift_start;
            $masterInsert['shift_end'] = $shift_end;
            $masterInsert['shift_id'] = $shift_id;
            $masterInsert['end_clientid'] = $masterInsert['end_clientid'].'-s';
            $masterFlag = $db->updateAsDict('staff_work_attendance', $masterInsert, ['conditions' => "id=?", 'bind' => [$masterAttendance['id']]]);
            $db->commit();

            $this->logger->write_log("sub_staff_support update masterFlag=$masterFlag, subFlag=$flag, masterdata=" .  json_encode($masterInsert, JSON_UNESCAPED_UNICODE), 'info');
        } catch (\Exception $e){
            $db->rollback();
            $this->getDI()->get('logger')->write_log("更新子账号和主账号考勤信息失败异常信息:" . $e->getMessage(), 'error');
            $flag = false;
        }
        return $flag;
    }

    /**
     * Notes: 缺卡、迟到早退提醒
     * @param array $param
     * @return array
     * @throws BusinessException
     */
    public function malformed(array $param): array
    {
        $this->logger->write_log(["malformed_data"=>$param], 'info');

        // 考勤日期当天
        $attendanceDate = empty($param['attendance_date']) ? date('Y-m-d') : $param['attendance_date'];
        // 考勤日期的昨天
        $yesterdayAttendanceDate = date('Y-m-d', strtotime($attendanceDate.' -1 days'));
        // 考勤日期的前天
        $beforeYesterdayAttendanceDate = date('Y-m-d', strtotime($attendanceDate.' -2 days'));

        $staffId = $param['user_info']['staff_id'];
        //  老版本app只有上班打完提醒缺卡
        $attendanceCategory = empty($param['attendance_category']) ? AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD : $param['attendance_category'];
        $result             = [
            // 补卡次数
            'reissue_card_count'           => 0,
            'attendance_start_dates'       => [],
            'attendance_end_dates'         => [],
            'is_need_reissue'              => 0,
            // 是否提醒迟到早退
            'have_late_early_alert'        => false,
            // 迟到早退提醒的内容
            'late_early_alert_msg'         => '',
            // 迟到早退提醒弹窗是否展示跳转进入出勤记录页的按钮
            'late_early_alert_view_detail' => false,
        ];
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffId,
            ],
            'columns'    => 'staff_info_id,node_department_id,working_country,job_title,is_sub_staff,hire_type',
        ]);

        if ($staffInfo && $staffInfo->is_sub_staff == 1) {
            return $result;
        }


        /************************** 缺卡提醒 ************************/
        // 上班卡才有缺卡提醒
        if ($attendanceCategory == AttendanceCalendarEnums::GO_WORK_MAKE_UP_CARD) {
            $attendanceDatas = AttendanceDataV2Model::find([
                'columns'    => 'staff_info_id, stat_date, attendance_started_at, attendance_end_at, shift_start, shift_end, display_data',
                'conditions' => ' staff_info_id = :staff_id: and stat_date in ({stat_date:array}) and AB != 0 ',
                'bind'       => [
                    'staff_id'  => $staffId,
                    'stat_date' => [$yesterdayAttendanceDate, $beforeYesterdayAttendanceDate],
                ],
            ])->toArray();

            $punchMonthCardNum            = (new AuditRepository($this->lang))->getAttendanceCarMonthData([
                'staff_id'          => $staffId,
                'reissue_card_date' => date('Y-m-d', time()),
            ]);
            $result['reissue_card_count'] = $punchMonthCardNum > 3 ? 0 : 3 - $punchMonthCardNum;

            $staffAuditReissueForBusinesses = StaffAuditReissueForBusinessModel::find([
                'conditions' => ' staff_info_id = :staff_id: and attendance_date in ({dates:array}) ',
                'bind'       => [
                    'staff_id' => $staffId,
                    'dates'    => [$yesterdayAttendanceDate, $beforeYesterdayAttendanceDate],
                ],
            ])->toArray();
            $staffAuditReissueForBusinesses = array_column($staffAuditReissueForBusinesses, null, 'attendance_date');
            $whiteListServer = new WhiteListServer();
            $whiteList = $whiteListServer->staffValidDateRange($staffId);
            foreach ($attendanceDatas as $attendanceData) {
                if ($whiteListServer->isInclude($whiteList,$attendanceData['stat_date'])) {
                    continue;
                }
                $displayDatas = explode('|', $attendanceData['display_data']);
                if (!$attendanceData['attendance_started_at']) {
                    if (!isset($staffAuditReissueForBusinesses[$attendanceData['stat_date']]) || empty($staffAuditReissueForBusinesses[$attendanceData['stat_date']]['start_time'])) {
                        if ($displayDatas && isset($displayDatas[0]) && $displayDatas[0] == 'AB') {
                            $result['is_need_reissue']          = 1;
                            $result['attendance_start_dates'][] = $attendanceData['stat_date'];
                        }
                    }
                }
                if (!$attendanceData['attendance_end_at']) {
                    if (!isset($staffAuditReissueForBusinesses[$attendanceData['stat_date']]) || empty($staffAuditReissueForBusinesses[$attendanceData['stat_date']]['end_time'])) {
                        if ($displayDatas && (count($displayDatas) == 1 || isset($displayDatas[1]) && $displayDatas[1] == 'AB')) {
                            $result['is_need_reissue']        = 1;
                            $result['attendance_end_dates'][] = $attendanceData['stat_date'];
                        }
                    }
                }
            }
        }

        /************************** 迟到早退提醒 ************************/

        //主播职位 不判断 迟到早退
        $liveJobId = (new SettingEnvServer())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        if($staffInfo && in_array($staffInfo->job_title, $liveJobId)){
            return $result;
        }

        if (!$staffInfo || !$this->getCanLateEarlyAlert($staffInfo, $attendanceCategory)) {
            return $result;
        }

        $masterStaffId = $this->getMasterStaffId($staffInfo);
        // 今天的打卡记录
        $getAttendanceStaffId = $staffId;
        if ($masterStaffId) {
            $getAttendanceStaffId = $masterStaffId;
        }
        $attendanceRepo = new AttendanceRepository($this->lang, $this->timezone);
        $attendanceInfo = $attendanceRepo->getStaffWorkAttendanceInfo($getAttendanceStaffId, $attendanceDate);
        // 必须是工作日，并且上下班的卡都已打了
        if ($attendanceInfo && $attendanceInfo->working_day == StaffWorkAttendanceModel::WORK_DAY_UN_PH_UN_REST && $attendanceInfo->started_at && $attendanceInfo->end_at) {
            // 获取当天应该执行的上班打卡时间和下班打卡时间，班次使用打卡记录表中固化的，与hcm保持一致
            $res = $this->getAttendanceStartEnd($staffId, $attendanceDate, $attendanceInfo->shift_start,
                $attendanceInfo->shift_end);
            if (!is_null($res)) {
                [$attendanceStartAt, $attendanceEndAt] = $res;

                [$state, $lateEarlyMin] = $this->getLateEarlyState(show_time_zone($attendanceInfo->started_at),
                    show_time_zone($attendanceInfo->end_at),
                    $attendanceStartAt, $attendanceEndAt);
                switch ($state) {
                    case AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_1 :
                        // 无迟到早退
                        break;
                    case AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_2:
                        // 迟到早退时长大于0分钟小于10分钟
                        $result['have_late_early_alert']        = true;
                        $result['late_early_alert_view_detail'] = false;
                        $result['late_early_alert_msg']         = $this->getTranslation()->_('attendance_late_early_0',
                            ['x' => $lateEarlyMin]);
                        break;
                    case AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_3:
                        // 大于10分钟小于等于1小时
                        $result['have_late_early_alert']        = true;
                        $result['late_early_alert_view_detail'] = true;
                        $result['late_early_alert_msg']         = $this->getTranslation()->_('attendance_late_early_1',
                            ['x' => $lateEarlyMin]);
                        break;
                    case AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_4:
                        // 大于1小时小于等于3小时
                        $result['have_late_early_alert']        = true;
                        $result['late_early_alert_view_detail'] = true;
                        $hour                                   = intdiv($lateEarlyMin, 60);
                        $min                                    = $lateEarlyMin % 60;
                        $result['late_early_alert_msg']         = $this->getTranslation()->_('attendance_late_early_2',
                            ['x' => $hour, 'y' => $min]);
                        break;
                    case AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_5:
                        // 大于3小时
                        $result['have_late_early_alert']        = true;
                        $result['late_early_alert_view_detail'] = true;
                        $hour                                   = intdiv($lateEarlyMin, 60);
                        $min                                    = $lateEarlyMin % 60;
                        $result['late_early_alert_msg']         = $this->getTranslation()->_('attendance_late_early_3',
                            ['x' => $hour, 'y' => $min]);
                        break;
                    default:
                }
            }
        }
        return $result;
    }

    /**
     * Notes: 下班卡才有迟到早退惩罚提醒，与hcm算薪功能配套，暂时只有印尼
     * @param $staffInfo
     * @param $attendanceCategory
     * @return bool
     */
    private function getCanLateEarlyAlert($staffInfo, $attendanceCategory): bool
    {
        // 检查是否是下班卡
        if ($attendanceCategory != AttendanceCalendarEnums::GET_OFF_WORK_MAKE_UP_CARD) {
            return false;
        }
        // 检查当前国家
        if (!isCountry('ID')) {
            return false;
        }


        // 检查用户的工作国家
        if ($staffInfo->working_country != HrStaffInfoModel::WORKING_COUNTRY_ID) {
            return false;
        }

        // 检查员工是否存在迟到早退
        if (!AttendanceAlertServer::checkStaffIsHaveLateEarly($staffInfo->staff_info_id, $staffInfo->node_department_id,
            $staffInfo->job_title)) {
            return false;
        }
        return true;
    }

    /**
     * Notes: 获取迟到早退时长所在的状态
     * @param $startedAt
     * @param $endAt
     * @param $attendanceStartAt
     * @param $attendanceEndAt
     * @return array
     *         state：1 无迟到早退 2 小于10分钟 3 大于10分钟小于等于1小时 4 大于1小时小于等于3小时 5 大于3小时
     *         lateEarlyMin：迟到早退的时长（分钟）
     */
    private function getLateEarlyState($startedAt, $endAt, $attendanceStartAt, $attendanceEndAt): array
    {
        $state        = AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_1;
        $lateMin      = $this->calculateLateTime($startedAt, $attendanceStartAt);
        $earlyMin     = $this->calculateEarlyTime($endAt, $attendanceEndAt);
        $lateEarlyMin = $lateMin + $earlyMin;
        if ($lateEarlyMin > 0) {
            if ($lateEarlyMin <= 10) {
                $state = AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_2;
            }
            if ($lateEarlyMin > 10 && $lateEarlyMin <= 60) {
                $state = AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_3;
            }

            if ($lateEarlyMin > 60 && $lateEarlyMin <= 180) {
                $state = AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_4;
            }

            if ($lateEarlyMin > 180) {
                $state = AttendanceEnums::ATTENDANCE_LATE_EARLY_MINUTE_5;
            }
        }
        return [$state, $lateEarlyMin];
    }

    /**
     * Notes: 计算迟到时长
     * @param  $startedAt
     * @param  $attendanceStart
     * @return false|float
     */
    private function calculateLateTime($startedAt, $attendanceStart)
    {
        // 迟到时长 打卡时间使用到秒，迟到早退时长转为分钟向上取整
        $lateTimeSec = max(strtotime($startedAt) - $attendanceStart, 0);
        // 迟到小于等于一分钟，不算迟到
        $lateTimeSec = $lateTimeSec <= 60 ? 0 : $lateTimeSec;
        return ceil($lateTimeSec / 60);
    }

    /**
     * Notes: 计算早退时长
     * @param  $endAt
     * @param  $attendanceEnd
     * @return false|float
     */
    private function calculateEarlyTime($endAt, $attendanceEnd)
    {
        // 早退时长  打卡时间使用到秒，迟到早退时长转为分钟向上取整
        $earlyTimeSec = max($attendanceEnd - strtotime($endAt), 0);
        // 早退小于等于一分钟，不算早退
        $earlyTimeSec = $earlyTimeSec <= 60 ? 0 : $earlyTimeSec;
        return ceil($earlyTimeSec / 60);
    }

    /**
     * Notes:
     * @param $staffInfo
     * @return int|mixed
     * @throws BusinessException
     */
    public function getMasterStaffId($staffInfo)
    {
        $masterStaffId = 0;
        $isOpen        = (new SettingEnvServer())->getSetVal(self::SUB_STAFF_SUPPORT_SWITCH_KEY);
        $this->logger->write_log("sub_staff_support isOpen=$isOpen", 'info');

        $attendanceRepo = new AttendanceRepository($this->lang, $this->timezone);
        if ($isOpen == AttendanceServer::SUB_STAFF_SUPPORT_OPEN) {
            // 校验子账号打卡是否在支援期间内
            $isSubStaff       = $staffInfo->is_sub_staff ?? 0;
            $supportStaffInfo = [];
            if ($isSubStaff) {
                $supportStaffInfo = $attendanceRepo->getSupportInfoBySubStaff($staffInfo->staff_info_id);
                if (empty($supportStaffInfo['staff_info_id'])) {
                    throw new BusinessException($this->getTranslation()->_('staff_support_expired'));
                }
                $masterStaffId = $supportStaffInfo['staff_info_id'];
            }

            $this->logger->write_log("sub_staff_support masterStaffId=$masterStaffId, isSubStaff=$isSubStaff, supportinfo=".json_encode($supportStaffInfo,
                    JSON_UNESCAPED_UNICODE), 'info');
        }

        return $masterStaffId;
    }

    /**
     * Notes: 获取考勤日期当天的上班和下班时间
     *        返回int时间戳，两个半天或者请全天假返回null
     * @param $staffId
     * @param $attendanceDate
     * @param $shiftStart
     * @param $shiftEnd
     * @return float[]|int[]|null
     */
    public function getAttendanceStartEnd($staffId, $attendanceDate, $shiftStart, $shiftEnd): ?array
    {
        // 准时打卡的上班时间
        $attendance_start_at = strtotime($attendanceDate.' '.$shiftStart);
        // 准时打卡的下班时间
        $attendance_end_at = strtotime($attendanceDate.' '.$shiftEnd);
        // 获取请假情况
        $auditRepository = new AuditRepository($this->lang);
        $leaveInfo       = $auditRepository->getStaffLeaveInfoByDate($staffId, $attendanceDate);
        if (!empty($leaveInfo)) {
            // 请了两个半天假
            if (count($leaveInfo) > 1) {
                return null;
            }
            $leaveInfo = current($leaveInfo);
            // 请了全天假
            if ($leaveInfo['type'] == 0) {
                return null;
            }
            // 请了上午假，上班时间延后5小时
            if ($leaveInfo['type'] == 1) {
                $attendance_start_at = strtotime($attendanceDate.' '.$shiftStart) + (5 * 3600);
            }
            // 请了下午假，下班时间提前5小时
            if ($leaveInfo['type'] == 2) {
                $attendance_end_at = strtotime($attendanceDate.' '.$shiftEnd) - (5 * 3600);
            }
        }
        return [$attendance_start_at, $attendance_end_at];
    }


    //新增需求 出差期间 必须打卡 并且 非真实写入打卡表 需要走审批流程
    public function check_on_trip($param)
    {
        $staff_id = $param['staff_info_id'];
        $shift_start = $param['shift_start'];
        $shift_end = $param['shift_end'];
        $shift_id = $param['shift_id'] ?? 0;

        //判断是否已经存在 出差打卡申请 记录
        $ab_server = new AttendanceBusinessServer($this->lang,$this->timezone);
        $check_info = $ab_server->find_by_date($staff_id,$param['attendance_date']);
        if(!empty($check_info)){
            //判断 上班存在
            if(!empty($check_info['start_time']) && $param['attendance_category'] == 1){
                $return['code'] = -1;
                $return['message'] = $this->getTranslation()->_('business_card_notice');
                $return['data'] = null;
                return $return;
            }
            //下班存在
            if(!empty($check_info['end_time']) && $param['attendance_category'] == 2){
                $return['code'] = -1;
                $return['message'] = $this->getTranslation()->_('business_card_notice');
                $return['data'] = null;
                return $return;
            }
        }

        //判断是否需要根据时区调整时间 https://flashexpress.feishu.cn/docx/NuQpdsXRSoSJtxxt0AKcaEwanPb
        //获取出差打卡时的时区
        $this->logger->write_log("getBusinessAttendanceTimeZone {$staff_id} " . json_encode($param),'info');

        $time_zone = $this->getBusinessAttendanceTimeZone($param);  //时区默认是 当地时区




        //保存补卡申请操作
        $bus_insert['staff_info_id'] = $staff_id;
        $bus_insert['attendance_date'] = $param['attendance_date'];

        if (empty($check_info) || empty($check_info->shift_start)) {
            $bus_insert['start_shift'] = $shift_start;
            $bus_insert['end_shift']   = $shift_end;
            $bus_insert['shift_id']    = $shift_id;
        }
        $bus_insert['working_day'] = $param['working_day'];
        //第二天 当地时间中午12点 跑任务
        $bus_insert['task_time'] = date('Y-m-d 12:00:00',strtotime("+1 day"));
        $bus_insert['business_trip_type'] = $param['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL;

        if($param['attendance_category'] == 1){//上班
            $bus_insert['start_time'] = gmdate('Y-m-d H:i:s',time());
            $bus_insert['start_lat'] = $param['click_lat'];
            $bus_insert['start_lng'] = $param['click_lng'];
            $bus_insert['started_path'] = $param['click_url'];
            $bus_insert['started_bucket'] = $this->getDI()['config']['application']['oss_bucket'];//固定
            $bus_insert['start_reason'] = empty($param['remark']) ? '' : $param['remark'];
            $bus_insert['start_time_zone'] = $time_zone;
            $bus_insert['started_os'] = $param['os'];
            $bus_insert['started_clientid'] = $param['clientid'];
        }else{//下班
            $bus_insert['end_time'] = gmdate('Y-m-d H:i:s',time());;
            $bus_insert['end_lat'] = $param['click_lat'];
            $bus_insert['end_lng'] = $param['click_lng'];
            $bus_insert['end_path'] = $param['click_url'];
            $bus_insert['end_bucket'] = $this->getDI()['config']['application']['oss_bucket'];
            $bus_insert['end_reason'] = empty($param['remark']) ? '' : $param['remark'];
            $bus_insert['end_time_zone'] = $time_zone;
            $bus_insert['end_os'] = $param['os'];
            $bus_insert['end_clientid'] = $param['clientid'];

        }
        $ab_server->apply_by_business($bus_insert,$check_info);
        $this->getDI()->get('logger')->write_log("出差期间外勤打卡操作 ".json_encode($bus_insert) ,'info');
        return true;
    }


    //获取同类型网点 最近的网点坐标 根据当前设备坐标  总部去配置 网点 取同类型

    /**
     * @param $lat 客户端位置
     * @param $lng
     * @param array $store_list 限制的 哪些网点获取
     * @param $category 限制分类
     * @return mixed
     */
    public function get_store_location($lat, $lng,$store_list = array() ,$category = 0)
    {

        /**
         * java map
         * SELECT
         *,
        (6371000 *
        acos(cos(radians(#{lat})) * cos(radians(lat)) * cos(radians(lng) - radians(#{lng})) + sin(radians(#{lat})) * sin(radians(lat)))) AS distance
        FROM sys_store where lat is not null and lng is not null and state = 1 and category not in (3,6)
        <if test="storeIds != null and storeIds.size() > 0">
        and id not in
        <foreach close=")" collection="storeIds" index="index" item="id" open="(" separator=",">
        #{id,jdbcType=VARCHAR}
        </foreach>
        </if>
        <if test="allowedStoreIds != null and allowedStoreIds.size() > 0">
        and id in
        <foreach close=")" collection="allowedStoreIds" index="index" item="id" open="(" separator=",">
        #{id,jdbcType=VARCHAR}
        </foreach>
        </if>
        <if test="storeCategories != null and storeCategories.size() > 0">
        and category IN
        <foreach close=")" collection="storeCategories" index="index" item="storeCategory" open="(" separator=",">
        #{storeCategory}
        </foreach>
        </if>
        ORDER BY distance limit 1
         */

        //enums::$store_category;

        if(!empty($category)){
            $category_arr = array();
            switch ($category) {
                case 5:
                case 4:
                    $category_arr[] = 4;
                    $category_arr[] = 5;
                    break;
                case 7:
                    $category_arr[] = 7;
                    break;
                case 8:
                    $category_arr[] = 8;
                    break;
                case 2:
                case 1:
                    $category_arr[] = 1;
                    $category_arr[] = 2;
                    break;
                default:
            }
            $category_str = implode(',',$category_arr);
        }


        $sql = "
                SELECT *,
                (6371000 * acos(cos(radians({$lat})) * cos(radians(lat)) * cos(radians(lng) - radians({$lng})) + sin(radians({$lat})) * sin(radians(lat)))) AS distance
                FROM sys_store where lat is not null and lng is not null 
                and state = 1 
                and category not in (3,6) 
                
                ";

        //目前没用到限制网点
//        if(!empty($store_list))
//            $sql .= " and id in (xxx) ";

        if(!empty($category_str))
            $sql .= " and category IN ({$category_str}) ";

        $sql .= " ORDER BY distance limit 1 ";

        $data = $this->getDI()->get('db_fle')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
        $return['lat'] = $data['lat'];
        $return['lng'] = $data['lng'];
        $return['store_id'] = $data['id'];
        return $return;
    }

    //计算总部的 几个坐标距离 返回最近 总部坐标
    function get_department_location($lat, $lng)
    {
        $earth_radius = 6371000;   //approximate radius of earth in meters
        //获取 总部的几个位置坐标
        $re = new AttendanceRepository($this->lang, $this->timezone);
        //总部 多个地点坐标数据
        $header = $re->get_header_range();
        $return = array();
        if(!empty($header)){
            foreach ($header as $v){
                $dLat = deg2rad( $v['lat'] - $lat);
                $dLon = deg2rad($v['lng'] - $lng);
                /*
                  Using the
                  Haversine formula
                  http://en.wikipedia.org/wiki/Haversine_formula
                  http://www.codecodex.com/wiki/Calculate_Distance_Between_Two_Points_on_a_Globe
                  验证：百度地图  http://developer.baidu.com/map/jsdemo.htm#a6_1
                  calculate the distance
                */
                $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat)) * cos(deg2rad($v['lat'])) * sin($dLon/2) * sin($dLon/2);
                $c = 2 * asin(sqrt($a));
                $d = round($earth_radius * $c);
                $return[$d]['lat'] = $v['lat'];
                $return[$d]['lng'] = $v['lng'];

            }
            //根据健 降序排 取最近的一个
            krsort($return);
        }

        if(!empty($return))
            return end($return);

        return array();
    }

    /**
     * @description 根据当前客户端位置坐标匹配坐标列表中最近的
     * @param $list
     * @param $lat
     * @param $lng
     * @param int $type 取最近还是最远 1 最近 2 最远
     * @return array
     */
    public function get_most_close($list,$lat,$lng, $type = 1)
    {
        if(empty($list)){
            $row['lat'] = $row['lng'] = $row['store_id'] = $row['range'] = '';
            return $row;
        }
        $earth_radius = 6371000;   //approximate radius of earth in meters
        foreach ($list as $v){
            $v['lat'] = floatval($v['lat']);
            $v['lng'] = floatval($v['lng']);

            $dLat = deg2rad( $v['lat']- $lat);
            $dLon = deg2rad($v['lng'] - $lng);
            $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat)) * cos(deg2rad($v['lat'])) * sin($dLon/2) * sin($dLon/2);
            $c = 2 * asin(sqrt($a));
            $d = round($earth_radius * $c);
            $return[$d]['lat'] = $v['lat'];
            $return[$d]['lng'] = $v['lng'];
            $return[$d]['store_id'] = empty($v['store_id']) ? '' : $v['store_id'];
            $return[$d]['range'] = empty($v['range']) ? '' : $v['range'];

        }
        if(empty($return)){
            return [];
        }
        //根据健 降序排 取最近的一个
        krsort($return);
        //取最远的
        if($type == 2){
            //还要距离
            $tmp = array_keys($return);
            $distance = array_shift($tmp);
            $farInfo = array_shift($return);
            $farInfo['distance'] = $distance;
            return $farInfo;
        }

        if (!empty($return)) {
            return end($return);
        }
        return array();
    }

    //保存人脸对比 地址和 对比结果 来源等
    public function add_face_log($user_info,$attendance_type,$param)
    {
        try{
            $f_param['staff_info_id'] = $user_info['staff_id'];
            $f_param['organization_id'] = $user_info['organization_id'];
            $f_param['organization_type'] = $user_info['organization_type'];
            $f_param['attendance_date'] = $param['attendance_date'];
            $f_param['verify_channel'] = $attendance_type;//验证渠道 阿里 0  腾讯 1 百度 2 ai 3 原图底片 4
            $f_param['success_enabled'] = $param['success_enabled'];
            $f_param['image_path'] = $param['click_url'];//验证渠道
            $f_param['image_bucket'] = $this->config->application->oss_bucket;
            $f_param['device_type'] = $param['device'];
            $this->getDI()->get('logger')->write_log("add_face_log f_param:".json_encode($f_param),'info');
            return $this->getDI()->get('db')->insertAsDict('staff_work_face_verify_record', $f_param);
        }catch (\Exception $e){
            $this->getDI()->get('logger')->write_log("add_face_log exception:".$e->getMessage(),'info');
        }
    }


    //新增逻辑 今天昨天没打卡 判定当前时间是否在 昨天上班班次 22小时之内
    protected function checkYesterdayLast($staffId, $date)
    {
        $today = date('Y-m-d');
        if ($date >= $today) {
            return false;
        }
        $shiftInfo = $this->shiftInfo[$date] ?? [];
        if (empty($shiftInfo)) {
            return false;
        }

        $start = $this->shiftInfo[$date]['start_datetime'];

        $currentTime = date('Y-m-d H:i:s');
        $endLast     = date('Y-m-d H:i:s', strtotime("{$start} +22 hour"));
        return $currentTime <= $endLast;
    }


    protected function makeResponseCode($isSet422)
    {
        if ($isSet422) {
            http_response_code(422);
        }
    }

    /**
     * @description AI人脸图像质量评估
     * @doc https://flashexpress.feishu.cn/docx/XSJsdo6n4o9LH0xdQS5cDNXonch
     * @param $image_url
     * @param $staff_info_id
     * @return array
     * @throws ValidationException
     */
    public function analyzeFace($image_url, $staff_info_id,$isSet422 = true)
    {

        if(env('close_analyze_face',0)){
            return true;
        }
        $format['bucket'] = $this->config->application->oss_bucket;
        $format['path']   = $image_url;
        if (RUNTIME == 'pro') {
            $flag = 1;
        }

        $url = $this->format_oss($format, $flag ?? 0);
        $params = "url={$url}&face_attributes=quality,mask&max_face_num=2";
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_FACE_QUALITY)->send($params, $staff_info_id);

        //无返回结果 || 返回参数有问题
        if (empty($result) || !isset($result['result']['face_list'][0]['code']) || isset($result['error'])) {
            if (isset($result['error']) && $result['error']['code'] == 'NO_TARGET_DETECTED') {
            } else {
                $this->logger->write_log("quality check error:" . json_encode($result), "notice");
            }
            $this->makeResponseCode($isSet422);
            throw new ValidationException($this->getTranslation()->_('please_retry'), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        $detectCode = $result['result']['face_list'][0]['code'];
        if($result['result']['face_num'] != 1) {
            $this->makeResponseCode($isSet422);
            throw new ValidationException($this->getTranslation()->_('err_msg_multiple_faces'), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        //高质量图片定义：
        //同时满足face_num=1和code=0才算高质量
        if ($detectCode != AiStateEnums::ANALYZE_FACE_HIGH_QUALITY && $result['result']['face_num'] == 1) {
            switch ($detectCode) {
                case AiStateEnums::ANALYZE_FACE_WITH_MASK:
                    $message = "err_msg_face_with_mask";
                    break;
                case AiStateEnums::ANALYZE_FACE_BRIGHTNESS_NOT_ACCEPT:
                    $message = "err_msg_brightness_not_accept";
                    break;
                case AiStateEnums::ANALYZE_FACE_LOW_QUALITY:
                case AiStateEnums::ANALYZE_FACE_SHARPNESS_NOT_ACCEPT:
                    $message = "err_msg_image_low_quality";
                    break;
                case AiStateEnums::ANALYZE_FACE_INCOMPLETE:
                    $message = "err_msg_face_incomplete";
                    break;
                default:
                    $message = "4008";
                    break;
            }
            if($detectCode == AiStateEnums::ANALYZE_FACE_WITH_MASK && $this->validate_face_with_mask) {
                $this->makeResponseCode($isSet422);
                throw new ValidationException($this->getTranslation()->_($message), ErrCode::AI_IMAGE_VERIFY_ERROR);
            }

            //如果一定时间内超过三次则进行放过,可继续进行人脸校验
            $outsourcing_attendance_analyze_face_time = (new SettingEnvServer())->getSetValFromCache('outsourcing_attendance_analyze_face_time');
            if (!empty($outsourcing_attendance_analyze_face_time)) {
                $cache            = $this->getDI()->get('redis');
                $redis_key        = 'outsourcing_attendance_analyze_face_' . $staff_info_id;
                $redis_val        = $cache->get($redis_key);
                $redis_val        = empty($redis_val) ? [
                    'num'  => 0,
                    'time' => time() + $outsourcing_attendance_analyze_face_time,
                ] : json_decode($redis_val, true);
                $redis_val['num'] += 1;
                //获取失效时间
                $redis_time = $redis_val['time'] - time();
                if ($redis_time > 0) {
                    $cache->save($redis_key, json_encode($redis_val, JSON_UNESCAPED_UNICODE), $redis_time); //缓存
                } else {
                    $cache->delete($redis_key);
                    $redis_val['num'] = 0;
                }

                if ($redis_val['num'] > 3) {
                    return $result;
                }
            }
            $this->makeResponseCode($isSet422);
            throw new ValidationException($this->getTranslation()->_($message), ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        return $result;
    }

    /**
     * 设置 如果 戴口罩，一直提示
     */
    public function setValidateFaceWithMskTips()
    {
        $this->validate_face_with_mask = true;
    }

    /**
     * @description Ai人脸搜索
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function searchRepeatFace($paramIn)
    {
        $imageUrl       = $paramIn['source_url'] ?? "";
        $staffId        = $paramIn['staff_id'] ?? "";
        $storeId        = $paramIn['store_id'] ?? "";
        $groupIdList    = $paramIn['group_id_list'] ?? [];
        $source         = $paramIn['source'];
        $countryCode    = strtolower(env('country_code', 'Th'));

        //拼接图片地址
        $format['bucket'] = $this->config->application->oss_bucket;
        $format['path']   = $imageUrl;
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $url = $this->format_oss($format, $flag ?? 0);

        //生成唯一ID
        $nodeProvider = new RandomNodeProvider();
        $uuid = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();

        $params = [
            "url"           => $url,
            "request_id"    => $uuid,
            "country"       => $countryCode,
            "group_id_list" => $groupIdList,
        ];
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_SEARCH_FACE)->sendEx(json_encode($params), $staffId);
        if (empty($result)) {
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('4008'),
                ErrCode::AI_IMAGE_VERIFY_MISS_ARGS);
        }

        $model = new StaffWorkDetectFaceRecordModel();
        $model->setStaffInfoId($staffId);
        $model->setOrganizationId($storeId);
        $model->setAttendanceDate(date("Y-m-d"));
        $model->setOsSubmitFaceImagePath($this->format_oss($format));
        $model->setCreatedAt(date("Y-m-d H:i:s"));

        //搜索到人脸时
        //score字段大于等于93分，flash_hr_exist = true
        if (isset($result['result']['flash_hr_exist']) && $result['result']['flash_hr_exist']) { //发现比对的问题

            //检测到您已有正式员工账号，请勿使用外协/个人代理账号操作
            $message = [
                'store_id'      => $storeId,                    //所属网点
                'staff_info_id' => $staffId,                    //被识别出作弊的员工ID
                'detected_at'   => date("Y-m-d H:i:s"),  //当前时间
                'source'        => $source,                     //来源
            ];
            //发送消息
            $redis = $this->getDI()->get('redisLib');
            $content = json_encode($message, JSON_UNESCAPED_UNICODE);

            $this->logger->write_log("[searchRepeatFace lpush]" . json_encode($content, JSON_UNESCAPED_UNICODE), 'info');
            $redis->lpush(self::REDIS_SEARCH_STAFF_FACE_MESSAGE_KEY, $content);

            //获取匹配到的员工底片
            $matchStaffInfoId = $result['result']['person_id'];
            $matchStaffImageUrl = $this->get_face_img($matchStaffInfoId);
            if (!empty($matchStaffInfoId)) {
                $staffInfo = HrStaffInfoModel::findFirst([
                    "staff_info_id = :staff_info_id:",
                    "bind" => [
                        "staff_info_id" => $matchStaffInfoId,
                    ],
                    "columns" => "state,leave_date",
                ]);
                //如果离职超过30天，则不需要检测出来
                $envModel  = new SettingEnvServer();
                $leaveDays = $envModel->getSetVal('hris_os_leave_date_num');
                $leaveDays = is_numeric($leaveDays) ? $leaveDays : 30;
                $leaveDaysLimit = date('Y-m-d', strtotime(sprintf("-%d day", $leaveDays)));

                if (!empty($staffInfo) && ($staffInfo->state == HrStaffInfoModel::STATE_2 &&
                    $staffInfo->leave_date < $leaveDaysLimit)
                ) {
                    $model->setWorkAttendancePath($matchStaffImageUrl);
                    $model->setMatchStaffInfoId($matchStaffInfoId);
                    $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_NOT_MATCH);
                    $model->save();
                    $this->logger->write_log("StaffWorkDetectFaceRecordModel {$staffId} {$storeId}, leave has over 30 days", "info");

                    return $result;
                }
            }

            //save 外协员工提交的照片 + 匹配到的正式员工的底片
            $model->setWorkAttendancePath($matchStaffImageUrl);
            $model->setMatchStaffInfoId($matchStaffInfoId);
            $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH);
            $model->save();

            $this->logger->write_log("StaffWorkDetectFaceRecord {$staffId} {$storeId}, state = 1", "info");

            http_response_code(422);
            throw new ValidationException( $this->getTranslation()->_('err_msg_you_have_been_detected'),
                ErrCode::AI_IMAGE_VERIFY_ERROR);
        } else {
            $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_NOT_MATCH);
            $model->save();
            $this->logger->write_log("StaffWorkDetectFaceRecordModel {$staffId} {$storeId}, state = 0", "info");
        }

        return $result;
    }

    /**
     * @description 检验员工职位是否为快递员、编制是否为外协员工
     * @param $staff_info_id
     * @return bool 非快递员-false 快递员-true
     */
    public function checkStaffInfo($staff_info_id): bool
    {
        //校验是否为快递员、外协人员
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind' => [
                'staff_id' => $staff_info_id,
            ],
            'columns' => "job_title,formal,sys_store_id",
        ]);

        $jobTitle = $this->getCourierJobTitle();

        if (empty($staffInfo) || $staffInfo->formal != HrStaffInfoModel::FORMAL_0 ||
            !in_array($staffInfo->job_title, $jobTitle)) {

            //非快递员，无需校验
            return false;
        }
        return true;
    }

    /**
     * @description 检验员工职位是否为快递员、编制是否为个人代理
     * @param $staff_info_id
     * @return bool 非快递员-false 快递员-true
     */
    public function checkAgentStaffInfo($staff_info_id): bool
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind' => [
                'staff_id' => $staff_info_id,
            ],
            'columns' => "job_title,hire_type,sys_store_id",
        ]);
        $jobTitle = $this->getAgentCourierJobTitle();

        if (empty($staffInfo) || !in_array($staffInfo->hire_type, HrStaffInfoModel::$agentTypeTogether) ||
            !in_array($staffInfo->job_title, $jobTitle)) {

            //非快递员，无需校验
            return false;
        }
        return true;
    }

    /**
     * @description 获取职位
     * @return array
     */
    public function getCourierJobTitle()
    {
        if (isCountry("MY")) {
            $jobTitle = [
                enums::$job_title['bike_courier'],
                enums::$job_title['van_courier'],
                enums::$job_title['tricycle_courier'],
                enums::$job_title['boat_courier'],
                enums::$job_title['car_courier'],
            ];
        } else if (isCountry("TH")) {
            $jobTitle = [
                enums::$job_title['bike_courier'],
                enums::$job_title['van_courier'],
                enums::$job_title['tricycle_courier'],
                enums::$job_title['boat_courier'],
                enums::$job_title['van_feeder'],
            ];
        } elseif (isCountry('PH')) {
            $jobTitle = [
                enums::$job_title['bike_courier'],
                enums::$job_title['van_courier'],
                enums::$job_title['tricycle_courier'],
                enums::$job_title['boat_courier'],
            ];
        } else {
            $jobTitle = [];
        }
        return $jobTitle;
    }

    /**
     * @return array
     */
    public function getAgentCourierJobTitle()
    {
        $positions = (new SettingEnvServer())->getSetValFromCache('backyard_check_position', ',');
        return $positions;
    }

    /**
     * @description 获取大区Id
     * @param string $store_id 员工所属的网点
     * @return array
     */
    public function getRegionList($store_id = null)
    {
        $model = new StaffWorkAttendanceRepository();
        if (!is_null($store_id)) {
            $storeInfo = SysStoreModel::findFirst([
                "id = :store_id:",
                "bind" => [
                    "store_id" => $store_id,
                ],
                "columns" => "id,manage_region",
            ]);
            return !empty($storeInfo) ? [$storeInfo->manage_region] : [];
        } else {
            return $model->getAllRegionsFromCache() ?? [];
        }
    }

    /**
     * @description 校验是否需要2小时校验
     * @param int $staff_info_id 外协员工工号
     * @return bool
     */
    public function checkIsNeedVerify($staff_info_id, $attendance_date = '')
    {
        if(empty($attendance_date)){
            $attendance_date = date("Y-m-d");
        }
        //查到最新一条记录
        $lastMatchedRecord = StaffWorkDetectFaceRecordModel::findFirst([
            "staff_info_id = :staff_info_id: and attendance_date = :attendance_date:",
            "bind" => [
                "staff_info_id" => $staff_info_id,
                "attendance_date" => $attendance_date,
            ],
            "columns" => 'MAX(created_at) as created_at',
        ]);
        if (empty($lastMatchedRecord)) {
            return true;
        }

        //当日22点以后不再弹检验
        $checkTime = strtotime(date("Y-m-d 22:00:00"));
        if (get_runtime() == 'pro' && $checkTime < time()) {
            return false;
        }

        $staffInfo = (new StaffRepository())->getStaffInfoOne($staff_info_id, 'hire_type');

        if (isCountry('TH') || isCountry('PH')) {
            if (!empty($staffInfo) && in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                //从 env 获取检测时间区间  // 没取到就 2 小时
                $attendance_should_detect_time = (new SettingEnvServer())->getSetValFromCache('backyard_check_seconds');
                $attendance_should_detect_time = empty($attendance_should_detect_time) ? 4 * 3600 : $attendance_should_detect_time;
            } else {
                //从 env 获取检测时间区间  // 没取到就 2 小时
                $attendance_should_detect_time = (new SettingEnvServer())->getSetValFromCache('outsourcing_attendance_should_detect_time');
                $attendance_should_detect_time = empty($attendance_should_detect_time) ? 4 * 3600 : $attendance_should_detect_time;
            }
        } else {
            //从 env 获取检测时间区间  // 没取到就 2 小时
            $attendance_should_detect_time = (new SettingEnvServer())->getSetValFromCache('outsourcing_attendance_should_detect_time');
            $attendance_should_detect_time = empty($attendance_should_detect_time) ? 4 * 3600 : $attendance_should_detect_time;
        }

        //最后一次检测时间 + 2小时
        $shouldDetectTime = strtotime($lastMatchedRecord->created_at) + $attendance_should_detect_time;

        if ($shouldDetectTime < time()) {
            return true;
        }

        return false;
    }


    //验证 对应日期 是不是 休息日 ph  或者 请没请假
    protected function checkRestDate($staffId,$date)
    {
        // 如果是休息日，则当前打卡时间记录到当前日期 如果不是休息日 按照原来逻辑走
        $leaveServer = new LeaveServer($this->lang, $this->timezone);
        $leaveServer = Tools::reBuildCountryInstance($leaveServer, [$this->lang, $this->timezone]);
        $restDates   = $leaveServer->staff_off_days($staffId, $date);
        if (!empty($restDates) && in_array($date, $restDates)) {
            return true;
        }

        //休息日 没有 再看看 请假没
        $p['staff_id']         = $staffId;
        $p['leave_start_time'] = $p['leave_end_time'] = $date;
        $auditRe               = new AuditRepository($this->lang);
        $isRest                = $auditRe->getLeaveData($p, enums::APPROVAL_STATUS_APPROVAL);

        //新增逻辑 请半天 不算 必须得请一天
        if (!empty($isRest)) {
            $typeSum = array_sum(array_column($isRest, 'sum_type'));
            if ($typeSum == 0 || $typeSum == 3) {//一整天 或者 2个半天 都算请一天
                return true;
            }
        }

        return false;
    }

    /**
     * 获取 主播 应工作时长
     *
     * 仅主播职位 使用
     *
     * @param $date
     * @param $attendance_started_at
     * @param $leaveType
     * @return float|int
     */
    public function getLiveHours($date, $attendance_started_at, $leaveType)
    {
        $hour = empty($attendance_started_at) ? null : date('H', strtotime($attendance_started_at));
        //日期是今天 返回当前时间的 如果不是今天 并且没打上班卡 就显示空
        if ($date == date('Y-m-d') && empty($hour)) {
            $hour = date('H');
        }

        $dayHour   = $this->free_shift_day_shift_duration;
        $nightHour = $this->free_shift_night_shift_duration;
        //4-16点 白班
        if ($hour >= 4 && $hour < 16) {
            $shift_hour     = $dayHour;
        } else {
            $shift_hour     = $nightHour;
        }

        if(!empty($leaveType) && in_array($leaveType, [$leaveType == AttendanceCalendarEnums::LEAVE_UP_TYPE, AttendanceCalendarEnums::LEAVE_DOWN_TYPE])) {
            $shift_hour =  $shift_hour / 2;
        }

        return $shift_hour;
    }

    /**
     * 班次
     * @param $staff_id
     * @param $job_title
     * @param $hire_type
     * @param $date_at
     * @param $attendance_started_at
     * @param $shift_start
     * @param $shift_end
     * @return array
     */
    public function makeLeaveTipsAndShiftInfo(
        $staff_id,
        $job_title,
        $hire_type,
        $date_at,
        $attendance_started_at,
        $shift_start,
        $shift_end
    ): array {
        $t         = $this->getTranslation();
        $date      = $date_at;
        $leaveInfo = (new LeaveServer($this->lang, $this->timezone))->getStaffLeaveInfo($staff_id, $date);
        $isOffRest = (new StaffOffDayRepository($this->lang, $this->timezone))->checkStaffIsOffRest($staff_id, $date);
        //新增班次信息
        $shift_str = $leave_tips = '';
        //是否主播
        if (in_array($job_title, $this->free_shift_position)) {
            $hour = empty($attendance_started_at) ? null : show_time_zone($attendance_started_at, 'H');
            //日期是今天 返回当前时间的 如果不是今天 并且没打上班卡 就显示空
            if ($date == date('Y-m-d') && empty($hour)) {
                $hour = date('H');
            }

            $dayHour   = $this->free_shift_day_shift_duration;
            $nightHour = $this->free_shift_night_shift_duration;
            //4-16点 白班
            if ($hour >= 4 && $hour < 16) {
                $shift_hour     = $dayHour;
                $live_shift_key = 'live_day_shift';
            } else {
                $shift_hour     = $nightHour;
                $live_shift_key = 'live_night_shift';
            }
            if ($isOffRest) {
                $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour]);
                $leave_tips = $t->_('att_show_shift_off_day');
                return [empty($hour) ? '' :$shift_str, $leave_tips];
            }
            switch ($leaveInfo) {
                case 3://请全天假
                    $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour]);
                    $leave_tips = $t->_('att_show_shift_all_day_leave');
                    break;
                case 2://请后半天假
                    $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour / 2]);
                    $leave_tips = $t->_('att_show_shift_2st_half_day_leave');
                    break;
                case 1://请前半天假
                    $shift_str  = $t->_($live_shift_key, ['hour' => $shift_hour / 2]);
                    $leave_tips = $t->_('att_show_shift_1st_half_day_leave');
                    break;
                default:
                    $shift_str = $t->_($live_shift_key, ['hour' => $shift_hour]);
                    break;
            }
            return [empty($hour) ? '' :$shift_str, $leave_tips];
        }

        if (!empty($shift_start)) {
            $att_show_shift_off_day            = 'att_show_shift_off_day';
            $att_show_shift_all_day_leave      = 'att_show_shift_all_day_leave';
            $att_show_shift_2st_half_day_leave = 'att_show_shift_2st_half_day_leave';
            $att_show_shift_1st_half_day_leave = 'att_show_shift_1st_half_day_leave';

            if (in_array($hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                $att_show_shift_off_day            = 'att_show_shift_off_day_agent';
                $att_show_shift_all_day_leave      = 'att_show_shift_all_day_leave_agent';
                $att_show_shift_2st_half_day_leave = 'att_show_shift_2st_half_day_leave_agent';
                $att_show_shift_1st_half_day_leave = 'att_show_shift_1st_half_day_leave_agent';
            }
            $shift_str = "$shift_start-$shift_end";
            if ($isOffRest) {
                $leave_tips = $t->_($att_show_shift_off_day);
                return [$shift_str, $leave_tips];
            }
            switch ($leaveInfo) {
                case 3://（请假-全天，无需打卡）
                    $shift_str  = "$shift_start-$shift_end";
                    $leave_tips = $t->_($att_show_shift_all_day_leave);
                    break;
                case 2://（请假-后半天）上班时间 到 上班时间+4小时
                    $end_shift  = date('H:i', strtotime($date . ' ' . $shift_start . ' +4 Hours'));
                    $shift_str  = "$shift_start-$end_shift";
                    $leave_tips = $t->_($att_show_shift_2st_half_day_leave);
                    break;
                case 1://（请假-前半天）下班时间-4小时 到 下班时间
                    $start_shift = date('H:i', strtotime($date . ' ' . $shift_end . ' -4 Hours'));
                    $shift_str   = "$start_shift-$shift_end";
                    $leave_tips  = $t->_($att_show_shift_1st_half_day_leave);
                    break;
                default:
                    break;
            }
        }

        return [$shift_str, $leave_tips];
    }

    /**
     * @param $staff_id
     *     1. 上班打卡时间，在班次周期（几点到几点）内，按班次的上班时间所在日期记录考勤日期；
    2. 上班打卡时间，不在班次周期内，当前打卡时间+4小时，如果在班次时间内，按班次的上班时间所在日期记录考勤；否则按打卡时间所在的日期记考勤。（如：班次为00:00-9:00，上班打卡时间是7月1号的23:30，记录考勤日期为7月2号。）
     */
    public function check_shift_date($staff_id)
    {
        $this->getDI()->get('logger')->write_log(['staff_id'=>$staff_id,'shiftInfo'=>$this->shiftInfo], 'info');
        $date        = date('Y-m-d');
        if (empty($this->shiftInfo[$date])) {
            return $date;
        }
        $start        = $this->shiftInfo[$date]['start_datetime'];
        $end          = $this->shiftInfo[$date]['end_datetime'];
        $current_time = date('Y-m-d H:i:s');
        //在区间之内 取当天
        if ($current_time > $start && $current_time < $end) {
            return date('Y-m-d', strtotime($start));
        }
        //https://flashexpress.feishu.cn/docs/doccnqeZXsN2fx8qT9DdbhleEQe
        $minute = 4.5 * 60;
        if ($current_time < $start) {
            //班次换前一天
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            if(empty($this->shiftInfo[$yesterday])){
                //没班次 返回当天
                return $date;
            }

            $start = $this->shiftInfo[$yesterday]['start_datetime'];
            $end   = $this->shiftInfo[$yesterday]['end_datetime'];
            if ($current_time > $start && $current_time < $end) {
                return date('Y-m-d', strtotime($start));
            }
            //如果还不在 +4.5 小时
            $c_cur = date('Y-m-d H:i:s', strtotime("-{$minute} minute"));
            if ($c_cur > $start && $c_cur < $end) {
                return date('Y-m-d', strtotime($start));
            }
        }

        //如果在班次区间后 返回后一天的
        if ($current_time > $end) {
            $tomorrow = date('Y-m-d', strtotime('+1 day'));
            if(empty($this->shiftInfo[$tomorrow])){
                //没班次 返回当天
                return $date;
            }
            $start    = $this->shiftInfo[$tomorrow]['start_datetime'];
            $end      = $this->shiftInfo[$tomorrow]['end_datetime'];
            //如果 当前时间  在 开始时间 -4.5 小时 和 结束时间内 就返回第二天日期
            $c_cur = date('Y-m-d H:i:s', strtotime("+4 hour"));
            if ($c_cur > $start && $c_cur < $end) {
                return date('Y-m-d', strtotime($start));
            }
        }
        return $date;
    }

    //保存 出差打卡的 审批通过的记录

    /**
     * @param $att_info 已经存在的 考勤记录
     * @param $bus_info 出差打卡的考勤记录
     * @return bool
     */
    public function save_attendance_business($att_info,$bus_info)
    {
        $insert = array();
        //上班缺失
        if (empty($att_info['started_at']) || (!empty($bus_info['start_time']) && strtotime($att_info['start_data']) > strtotime($bus_info['start_time']))) {
            $insert['started_at']        = $bus_info['start_time'];
            $insert['started_state']     = AttendanceBusinessServer::getStaffWorkAttendanceStateByBusinessTripType($bus_info['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL);
            $insert['started_staff_lat'] = $bus_info['start_lat'];
            $insert['started_staff_lng'] = $bus_info['start_lng'];
            $insert['started_path']      = $bus_info['started_path'];
            $insert['started_bucket']    = $bus_info['started_bucket'];
            $insert['started_remark']    = $bus_info['start_reason'];
            $insert['started_os']        = $bus_info['started_os'];
            $insert['started_clientid']  = $bus_info['started_clientid'];
        }
        //下班缺失
        if (empty($att_info['end_at']) || (!empty($bus_info['end_time']) && strtotime($att_info['end_data']) < strtotime($bus_info['end_time']))) {
            $insert['end_at']    = $bus_info['end_time'];
            $insert['end_state'] = AttendanceBusinessServer::getStaffWorkAttendanceStateByBusinessTripType($bus_info['business_trip_type'] ?? BusinessTripModel::BTY_NORMAL);;
            $insert['end_staff_lat'] = $bus_info['end_lat'];
            $insert['end_staff_lng'] = $bus_info['end_lng'];
            $insert['end_path']      = $bus_info['end_path'];
            $insert['end_bucket']    = $bus_info['end_bucket'];
            $insert['end_remark']    = $bus_info['end_reason'];
            $insert['end_os']        = $bus_info['end_os'];
            $insert['end_clientid']  = $bus_info['end_clientid'];
        }
        //没更新就直接返回
        if(empty($insert)){
            return true;
        }
        return $this->getDI()->get('db')->updateAsDict(
            'staff_work_attendance',
            $insert,
            'id = ' . $att_info['id']
        );

    }

    //部分员工 参与静默活体检测 测试
    public function get_live_list()
    {
        $code = 'live_list';
        $list = SettingEnvModel::findFirst("code = '{$code}'");
        if(!empty($list)){
            if ($list->set_val == 1) { //全员参与
                return true;
            } else {
                return explode(',', $list->set_val);
            }
        }
        return false;

    }


    /**
     *
     * 是否 可外勤打卡
     * if (OrganizationType.STORE.equals(staffInfo.getOrganizationType())) {
    SysStore sysStore = this.sysStoreDao.find(staffInfo.getOrganizationId());
    dto.setStoreLat(sysStore.getLat());
    dto.setStoreLng(sysStore.getLng());
    // 网点员工外勤白名单可以外勤打卡
    List<Integer> allowedFieldPunchStaffList = this.queryAllowedFieldPunchStaffList(ALLOWED_FIELD_PUNCH_STORE_STAFF);
    if (allowedFieldPunchStaffList.contains(staffInfo.getId())) {
    dto.setFieldPunch(true);
    LOGGER.info("Unusual field clocking record staffInfoId {} FieldPunch {}", staffInfo.getId(), true);
    } else {
    dto.setFieldPunch(false);
    }
    } else {
    SysConfiguration headLat = this.sysConfigurationDao.findByCfgKey(COURIER_BACKYARD_LAT);
    SysConfiguration headLng = this.sysConfigurationDao.findByCfgKey(COURIER_BACKYARD_LNG);
    if (headLat == null || headLng == null) {
    LOGGER.error(" Database versionLat information is empty");
    throw new ServiceEcaException(ErrorCodeEnum.C100305);
    } else {
    dto.setStoreLat(new BigDecimal(headLat.getCfgValue()));
    dto.setStoreLng(new BigDecimal(headLng.getCfgValue()));
    }
    //总部默认不允许打外勤卡
    dto.setFieldPunch(false);
    //如果开关打开允许总部所有员工外勤卡
    if (Boolean.TRUE.equals(sysConfigurationService.getHeaderOfficeFieldPunchSwitch())) {
    dto.setFieldPunch(true);
    } else {
    List<String> jobTitleIds = sysConfigurationService.getHeadOfficeFieldPunchJobTitleIds();
    List<String> departmentIds = sysConfigurationService.getHeadOfficeFieldPunchDepartmentIds();
    //白名单允许外勤卡/总部指定职位允许打外勤卡
    if (sysConfigurationService.getHeaderOfficeFieldPunchStaff().contains(String.valueOf(staffInfo.getId()))
    || (nonNull(staffInfo.getJobTitle()) && jobTitleIds.contains(String.valueOf(staffInfo.getJobTitle())))
    || ((nonNull(staffInfo.getDepartmentId()) && departmentIds.contains(staffInfo.getDepartmentId())))) {
    dto.setFieldPunch(true);
    }
    }
    }
     * @param $staff_info
     * @return boolean
     */
    public function get_punch_field($staff_info)
    {
        //培训需求，职位13的在tra环境开启外勤
        if (in_array(RUNTIME, ['tra', 'training']) && $staff_info['job_title'] == 13) {
            return true;
        }

        // 外协的外勤
        $attendanceServer = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        if($staff_info['formal'] == HrStaffInfoModel::FORMAL_0 && $attendanceServer->outsourceStaffPunchField($staff_info['staff_info_id'])){
            return true;
        }


        $attendanceRepository = new AttendanceRepository($this->lang, $this->timezone);
        $departmentList       = $attendanceRepository->getDepartmentConfigListById($staff_info['department_id']);
        foreach ($departmentList as $v) {
            if ($v['job_title_id'] == AttendanceServer::ALL_JOB_TITLE_OF_DEPARTMENT || ($v['job_title_id'] == $staff_info['job_title'])) {
                return true;
            }
        }
        $staffInfo = $attendanceRepository->getStaffConfigById($staff_info['id']);
        return !empty($staffInfo);
    }

    /**
     * 外协员工 外勤权限；
     * @param $staff_info
     * @return bool
     */
    public function outsourceStaffPunchField($staff_info): bool
    {
        if(!isCountry('PH')){
            return false;
        }

        //检查是否是公司外协
        $staffItem = HrStaffItemsModel::findFirst([ 'conditions' => "staff_info_id = :staff_info_id: and item = 'OUTSOURCING_TYPE'",
                                                    'bind' => ['staff_info_id' => $staff_info['id']],
                                                    'columns' => ['value']]);
        if(empty($staffItem) || $staffItem->value != 'company'){
            return  false;
        }

        //检查配置文件
        $config = (new SettingEnvServer())->listByCode([
            'outsource_field_punch_store',
            'outsource_field_punch_division_authority',
        ]);

        if (empty($config)) {
            return false;
        }

        $config = array_column($config, 'set_val', 'code');
        if (!empty($config['outsource_field_punch_store']) && in_array($staff_info['organization_id'],
                explode(',', $config['outsource_field_punch_store']))) {
            return true;
        }
        //检查网点信息
        if (empty($staff_info['store_info'])) {
            return false;
        }
        //所属分支机构
        $outsource_field_punch_division_authority = explode('/', $staff_info['store_info']['ancestry']);

        if (!empty($config['outsource_field_punch_division_authority']) && in_array(end($outsource_field_punch_division_authority),
                explode(',', $config['outsource_field_punch_division_authority']))) {
            return true;
        }
        return false;
    }



    //获取特殊配置的 网点
    public function get_special_store($staff_info,$setting)
    {
        if(empty($setting))
            return [];
        $setting = explode(',',$setting);
        $store_ids = array();

        //不符合条件
        if(!in_array($staff_info['job_title'],$setting))
            return $store_ids;


        //获取管辖范围
        $re = new StaffWorkAttendanceRepository($this->lang);
        $store_ids = $re->getManagerStoreFromCache($staff_info['id']);

        return $store_ids;
    }


    /**
     * 员工贷  员工注册
     *
     * @param $staffId
     */
    public function staffInfo($staffId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ]);
        $result = [];
        if ($staffInfo) {
            $staffInfo = $staffInfo->toArray();
            $result['mobile'] = $staffInfo['mobile'];
            $result['mobile_company'] = $staffInfo['mobile_company'];
            $result['employeeNo'] = $staffInfo['staff_info_id'];
            $result['email'] = $staffInfo['personal_email'];
            $hrStaffItems = HrStaffItemsModel::find([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind' => [
                    'staff_id' => $staffId,
                ],
            ])->toArray();
            if ($hrStaffItems) {
                $items = [];
                foreach ($hrStaffItems as $hrStaffItem) {
                    $items[$hrStaffItem['staff_info_id']][$hrStaffItem['item']] = $hrStaffItem['value'];
                }
                $province_list = $this->provinceList();
                $city_list = $this->cityList();
                $district_list = $this->districtList();

                // 户口所在地
                $row['register_province'] = $items[$staffId]['REGISTER_PROVINCE'] ?? '';//户口所在省
                $row['register_province_name'] =  !empty($row['register_province']) ? ($province_list[$row['register_province']]['name'] ?? '') : '';

                $row['register_city'] = $items[$staffId]['REGISTER_CITY'] ?? '';//户口所在市
                $row['register_city_name'] =  !empty($row['register_city']) ? ($city_list[$row['register_city']]['name'] ?? '') : '';

                $row['register_district'] = $items[$staffId]['REGISTER_DISTRICT'] ?? '';//户口所在乡
                $row['register_district_name'] =  !empty($row['register_district']) ? ($district_list[$row['register_district']]['name'] ?? '') : '';

                $row['register_postcodes'] = $items[$staffId]['REGISTER_POSTCODES'] ?? '';//户口所在邮编
                $row['register_house_num'] = $items[$staffId]['REGISTER_HOUSE_NUM'] ?? '';//户口所在门牌号
                $row['register_village_num'] = $items[$staffId]['REGISTER_VILLAGE_NUM'] ?? '';//户口所在村号
                $row['register_village'] = $items[$staffId]['REGISTER_VILLAGE'] ?? '';//户口所在村
                $row['register_alley'] = $items[$staffId]['REGISTER_ALLEY'] ?? '';//户口所在巷
                $row['register_street'] = $items[$staffId]['REGISTER_STREET'] ?? '';//户口所在街道

                // 居住地
                $row['residence_province'] = $items[$staffId]['RESIDENCE_PROVINCE'] ?? '';//居住地所在省
                $row['residence_province_name'] =  !empty($row['residence_province']) ? ($province_list[$row['residence_province']]['name'] ?? '') : '';

                $row['residence_city'] = $items[$staffId]['RESIDENCE_CITY'] ?? '';//居住地所在市
                $row['residence_city_name'] =  !empty($row['residence_city']) ? ($city_list[$row['residence_city']]['name'] ?? '') : '';

                $row['residence_district'] = $items[$staffId]['RESIDENCE_DISTRICT'] ?? '';//居住地所在乡
                $row['residence_district_name'] =  !empty($row['residence_district']) ? ($district_list[$row['residence_district']]['name'] ?? '') : '';

                $row['residence_postcodes'] = $items[$staffId]['RESIDENCE_POSTCODES'] ?? '';//居住地所在邮编
                $row['residence_house_num'] = $items[$staffId]['RESIDENCE_HOUSE_NUM'] ?? '';//居住地所在门牌号
                $row['residence_village_num'] = $items[$staffId]['RESIDENCE_VILLAGE_NUM'] ?? '';//居住地所在村号
                $row['residence_village'] = $items[$staffId]['RESIDENCE_VILLAGE'] ?? '';//居住地所在村
                $row['residence_alley'] = $items[$staffId]['RESIDENCE_ALLEY'] ?? '';//居住地所在巷
                $row['residence_street'] = $items[$staffId]['RESIDENCE_STREET'] ?? '';//居住地所在的街道

                $result['register_district'] = $row['register_district'];
                if($row['register_province'] == 'TH01') {
                    $result['address'] = 'บ้านเลขที่ '.$row['register_house_num'].' หมู่  '.$row['register_village_num'].' หมูบ้าน '.$row['register_village'].' ซอย '.$row['register_alley'].' ถนน '.$row['register_street'].' แขวง '.$row['register_district_name'].' เขต '.$row['register_city_name'].' จังหวัด'.$row['register_province_name'];
                } else {
                    if(empty($row['register_house_num'])
                        && empty($row['register_village_num'])
                        && empty($row['register_village'])
                        && empty($row['register_alley'])
                        && empty($row['register_street'])
                        && empty($row['register_district'])
                        && empty($row['register_city'])
                        && empty($row['register_province'])
                    ) {
                        $result['address'] = '';
                    } else {
                        $result['address'] = 'บ้านเลขที่ '.$row['register_house_num'].' หมู่ '.$row['register_village_num'].' หมูบ้าน '.$row['register_village'].' ซอย '.$row['register_alley'].' ถนน '.$row['register_street'].' ตำบล '.$row['register_district_name'].' อำเภอ '.$row['register_city_name'].' จังหวัด'.$row['register_province_name'];
                    }
                }

                $result['residence_district'] = $row['residence_district'];
                if($row['residence_province'] == 'TH01') {
                    $result['residentialAddress'] = 'บ้านเลขที่ '.$row['residence_house_num'].' หมู่ '.$row['residence_village_num'].' หมูบ้าน '.$row['residence_village'].' ซอย '.$row['residence_alley'].' ถนน '.$row['residence_street'].' แขวง '.$row['residence_district_name'].' เขต '.$row['residence_city_name'].' จังหวัด'.$row['residence_province_name'];
                } else {
                    if(empty($row['residence_house_num'])
                        && empty($row['residence_village_num'])
                        && empty($row['residence_village'])
                        && empty($row['residence_alley'])
                        && empty($row['residence_street'])
                        && empty($row['residence_district'])
                        && empty($row['residence_city'])
                        && empty($row['residence_province'])
                    ) {
                        $result['residentialAddress'] = '';
                    } else {
                        $result['residentialAddress'] = 'บ้านเลขที่ '.$row['residence_house_num'].' หมู่ '.$row['residence_village_num'].' หมูบ้าน '.$row['residence_village'].' ซอย '.$row['residence_alley'].' ถนน '.$row['residence_street'].' ตำบล '.$row['residence_district_name'].' อำเภอ '.$row['residence_city_name'].' จังหวัด'.$row['residence_province_name'];
                    }
                }

                $result['accountName'] = $items[$staffId]['BANK_NO_NAME'] ?? '';
                $result['accountNo'] = $staffInfo['bank_no']?? '';
                $result['accountBankCode'] = $staffInfo['bank_type'] && isset(self::BANK_TYPE_MAPS[$staffInfo['bank_type']]) ? self::BANK_TYPE_MAPS[$staffInfo['bank_type']] : '';
                $result['idCardNo'] = $staffInfo['identity']?? '';

                $result['emergencyContactName'] = ( $items[$staffId]['RELATIVES_FIRST_NAME'] ?? '' ) . ( $items[$staffId]['RELATIVES_LAST_NAME'] ?? '' );
                $result['emergencyContactMobile'] = $items[$staffId]['RELATIVES_MOBILE'] ?? '';
                $result['emergencyContactType'] = $items[$staffId]['RELATIVES_RELATIONSHIP'] ?? '';

                // 简历信息
                $resume = $this->resume($staffId);
                // 国籍
                $result['nationality'] = isset($items[$staffId]['NATIONALITY']) && isset(self::COUNTRY_CODES[$items[$staffId]['NATIONALITY']]) ? self::COUNTRY_CODES[$items[$staffId]['NATIONALITY']] : ''; //1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、99：其他
//                $result['nationality'] = $items[$staffId]['NATIONALITY'] ?? ''; //1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、99：其他
                $result['nationality_text'] = isset($items[$staffId]['NATIONALITY']) ? $this->getTranslation()->t('nationality_' . $items[$staffId]['NATIONALITY'])  : ''; //1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、99：其他
                // 出生日期
                $result['birthDate'] = $items[$staffId]['BIRTHDAY'] ?? ''; // 出生日期
                $result['thLastName'] = $resume && $resume['last_name'] ? $resume['last_name'] : '';
                $result['thFirstName'] = $resume && $resume['first_name'] ? $resume['first_name'] : '';

                $result['enLastName'] = $resume && $resume['last_name_en'] ? $resume['last_name_en'] : '';
                $result['enFirstName'] = $resume && $resume['first_name_en'] ? $resume['first_name_en'] : '';
            }

            $result['idCardFrontImg'] = $this->cardFrontImg($staffId); // 身份证正面照片
            $result['idCardBackImg'] = $this->cardBackImg($staffId); // 身份证背面照片
            $result['hireImg'] = $this->cardHireImg($staffId); // 入职底图照片
            $result['gender'] = $staffInfo['sex']; // 性别

            $result['storeArea'] = "";
            if ($staffInfo['sys_store_id'] != -1) {

                $sysStore = SysStoreModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => [
                        'id' => $staffInfo['sys_store_id'],
                    ],
                ]);
                $result['storeArea'] = $sysStore ? $sysStore->sorting_no : "";
            }
        }

        return $this->checkReturn(['data' => $result]);
    }

    private function cardHireImg($staffId)
    {
        $staffWorkAttendance = StaffWorkAttendanceAttachmentModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and deleted = :deleted: ',
            'bind' => [
                'staff_id' => $staffId,
                'deleted' => 0,
            ],
        ]);

        if ($staffWorkAttendance) {
            $staffWorkAttendance = $staffWorkAttendance->toArray();
            return $this->getPic('', $staffWorkAttendance['work_attendance_path']);
        }

        return '';

    }


    private function cardBackImg($staffId)
    {
        $hrAnnex = HrAnnexModel::findFirst([
            'conditions' => ' type = :type: and file_type = :file_type: and deleted = 0 and oss_bucket_key = :key: ',
            'bind' => [
                'type' => 1,
                'file_type' => 3,
                'key' => $staffId,
            ],
        ]);
        if ($hrAnnex) {
            $hrAnnex = $hrAnnex->toArray();
            return $this->getPic($hrAnnex['bucket_name'], $hrAnnex['object_key']);
        }

        return '';
    }


    private function cardFrontImg($staffId)
    {
        // 资产
        $hrAnnex = HrAnnexModel::findFirst([
            'conditions' => ' type = :type: and file_type = :file_type: and deleted = 0 and oss_bucket_key = :key: ',
            'bind' => [
                'type' => 4,
                'file_type' => 2,
                'key' => $staffId,
            ],
        ]);
        if ($hrAnnex) {
            $hrAnnex = $hrAnnex->toArray();
            return $this->getPic($hrAnnex['bucket_name'], $hrAnnex['object_key']);
        } else {
            // 简历
            $hrAnnex = HrAnnexModel::findFirst([
                'conditions' => ' type = :type: and file_type = :file_type: and deleted = 0 and oss_bucket_key = :key: ',
                'bind' => [
                    'type' => 1,
                    'file_type' => 2,
                    'key' => $staffId,
                ],
            ]);

            if ($hrAnnex) {
                $hrAnnex = $hrAnnex->toArray();
                return $this->getPic($hrAnnex['bucket_name'], $hrAnnex['object_key']);
            }
        }
        return '';
    }


    private function getPic($bucket_name,$object_key){
        $img_prefix = env("img_prefix","http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");

        return $img_prefix.$object_key;
        //return "http://".$bucket_name.'.oss-ap-southeast-1.aliyuncs.com/'.$object_key;
    }

    private function provinceList()
    {
        $province = SysProvinceModel::find([
            'columns' => ' code, name ',
            'conditions' => ' deleted = 0 ',
        ])->toArray();

        return array_column($province, null, 'code');
    }

    private function cityList()
    {
        $city = SysCityModel::find([
            'columns' => ' code, name, province_code ',
            'conditions' => ' deleted = 0 ',
        ])->toArray();
        return array_column($city, null, 'code');
    }

    private function districtList()
    {
        $districts = SysDistrictModel::find([
            'columns' => ' code, name, city_code, province_code ',
            'conditions' => ' deleted = 0 ',
        ])->toArray();

        return array_column($districts, null, 'code');
    }



    /**
     *
     * 员工贷 风控数据源
     *
     * @param $staffId
     * @param $month
     * @param $days
     * @param $startDate
     * @param $endDate
     *
     */
    public function staffLoansData($staffId, $month, $days, $startDate, $endDate)
    {
        //调用push接口
        $fle_rpc = (new ApiClient('bi_rpcv2','','staffloan.get_staff_loan_target', $this->lang));
        $fle_rpc->setParams([
            'staff_id' => $staffId,
            'month' => $month,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
        $ret =  $fle_rpc->execute();


        $this->getDI()->get('logger')->write_log('staff_loans_data ' . json_encode([
                'staff_id' => $staffId,
                'month' => $month,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ], JSON_UNESCAPED_UNICODE) . ' ' . json_encode($ret, JSON_UNESCAPED_UNICODE), 'info');

        if (isset($ret['result']) && $ret['result']['code'] == 1) {
            $result = $ret['result']['data'];
        }

        $hrStaffItems = HrStaffItemsModel::find([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ])->toArray();
        $items = [];
        if ($hrStaffItems) {
            foreach ($hrStaffItems as $hrStaffItem) {
                $items[$hrStaffItem['staff_info_id']][$hrStaffItem['item']] = $hrStaffItem['value'];
            }
        }

        // 是否发放警告书
        if ($startDate && $endDate) {

            $result['warning'] = $this->isHasWarningMessage($staffId, $startDate, $endDate);
        }
        // 入职天数
        $result['workDays'] = $this->workDays($staffId);

        if ($days) {

            $attendances = AttendanceDataV2Model::find([
                'conditions' => ' staff_info_id = :staff_id: and stat_date in ({days:array})',
                'bind' => [
                    'staff_id' => $staffId,
                    'days' => $days,
                ],
            ])->toArray();
            $attendances = array_column($attendances, null, 'stat_date');


            // 旷工
            $result['absenteeismCount'] = $this->absenteeismCount($attendances, $days);
            // 迟到
            $result['lateCount'] = $this->lateCount($attendances, $days);
            // 早退
            $result['leaveEarlyCount'] = $this->leaveEarlyCount($attendances, $days);

            foreach ($days as $day) {
                $result['absent'][$day] = [
                    'absenteeismCount' => $result['absenteeismCount'][$day],
                    'lateCount' => $result['lateCount'][$day],
                    'leaveEarlyCount' => $result['leaveEarlyCount'][$day],
                ];
            }

        }

        if ($month) {
            // 出勤天数
            $result['presentDaysMonthly'] = $this->attendanceDays($staffId, $month);
            // 旷工天数
            $result['absentDaysMonthly'] = $this->absentDays($staffId, $month);
//            // 考勤不合格天数
//            $result['unqualifiedDaysMonthly'] = $this->unqualifiedDays($staffId, $month);
            // 收到警告书次数
            $result['warningCntMonthly'] = $this->warningNums($staffId, $month);
        }

        // 是否在职
        $result['inService'] = $this->inService($staffId);


        // 国籍
        $result['nationality'] = isset($items[$staffId]['NATIONALITY']) && isset(self::COUNTRY_CODES[$items[$staffId]['NATIONALITY']]) ? self::COUNTRY_CODES[$items[$staffId]['NATIONALITY']] : ''; //1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、99：其他
        $result['nationality_text'] = isset($items[$staffId]['NATIONALITY']) ? $this->getTranslation()->t('nationality_' . $items[$staffId]['NATIONALITY'])  : ''; //1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、99：其他
        // 出生日期
        $result['birthDate'] = isset($items[$staffId]['BIRTHDAY'])
        && $items[$staffId]['BIRTHDAY']
        && DateTime::createFromFormat('Y-m-d', $items[$staffId]['BIRTHDAY']) !== false ? $items[$staffId]['BIRTHDAY'] : ''; // 出生日期

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ]);
        $result['storeArea'] = "";
        if ($staffInfo && $staffInfo->sys_store_id != -1) {

            $sysStore = SysStoreModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [
                    'id' => $staffInfo->sys_store_id,
                ],
            ]);
            $result['storeArea'] = $sysStore ? $sysStore->sorting_no : "";
        }
        return $this->checkReturn(['data' => $result]);
    }

    /**
     * 旷工
     *
     * @param $attendances
     * @param $days
     *
     * @return array
     */
    public function absenteeismCount($attendances, $days)
    {
        $result = [];
        foreach ($days as $day) {
            if (isset($attendances[$day])) {

                $result[$day] = $attendances[$day]['AB'] ? $attendances[$day]['AB']/ 10 : 0;
            } else {

                $result[$day] = 1;
            }
            $result[$day] = (string) $result[$day];
        }

        return $result;
    }

    /**
     *
     * @param $attendances
     * @param $days
     *
     * @return array
     */
    public function lateCount($attendances, $days)
    {
        $result = [];
        foreach ($days as $day) {
            if (isset($attendances[$day]) && $attendances[$day]['attendance_started_at'] && $attendances[$day]['shift_start']) {

                $startedAt = date('H:i', strtotime($attendances[$day]['attendance_started_at']));
                if ($startedAt > $attendances[$day]['shift_start']) {

                    $result[$day] = 1;
                } else {

                    $result[$day] = 0;
                }
            } else {

                $result[$day] = 0;
            }

            $result[$day] = (string) $result[$day];
        }

        return $result;

    }


    /**
     *
     * @param $attendances
     * @param $days
     * @return array
     *
     */
    public function leaveEarlyCount($attendances, $days)
    {
        $result = [];
        foreach ($days as $day) {
            if (isset($attendances[$day]) && $attendances[$day]['attendance_end_at'] && $attendances[$day]['shift_end']) {

                $endAt = date('H:i', strtotime($attendances[$day]['attendance_end_at']));
                if ($endAt < $attendances[$day]['shift_end']) {

                    $result[$day] = 1;
                } else {

                    $result[$day] = 0;
                }
            } else {

                $result[$day] = 0;
            }

            $result[$day] = (string) $result[$day];
        }

        return $result;

    }




    /**
     * 是否旷工
     *
     * @param $staffId
     * @param $days
     */
    private function absent($staffId, $days)
    {
        $attendances = AttendanceDataV2Model::find([
            'conditions' => ' staff_info_id = :staff_id: and stat_date in ({days:array})',
            'bind' => [
                'staff_id' => $staffId,
                'days' => $days,
            ],
        ])->toArray();
        // 1 旷工 2 正常 3 迟到 4 缺卡 5 早退
        $attendances = array_column($attendances, null, 'stat_date');

        $result = [];
        foreach ($days as $day) {
            $result[$day] = 1;
            if (isset($attendances[$day])) {
                if ($attendances[$day]['AB'] == 0) {
                    // 没有缺勤
                    $result[$day] = 2;

                } else {
                    // 旷工
                    $result[$day] = 1;
                    if (
                        !$attendances[$day]['attendance_started_at'] && $attendances[$day]['attendance_end_at']
                        ||
                        $attendances[$day]['attendance_started_at'] && !$attendances[$day]['attendance_end_at']
                    ) {
                        // 缺卡
                        $result[$day] = 4;
                    } else {
                        // 迟到或者早退
                        $startedAt = date('H:i', strtotime($attendances[$day]['attendance_started_at']));
                        $endAt = date('H:i', strtotime($attendances[$day]['attendance_end_at']));

                        if ($startedAt > $attendances[$day]['shift_start']) {
                            // 迟到
                            $result[$day] = 3;
                        }
                        if ($endAt < $attendances[$day]['shift_end']) {
                            // 早退
                            $result[$day] = 5;
                        }

                    }
                }
            }

        }

        return $result;
    }

    /**
     *
     * 简历
     *
     * @param $staffId
     * @return array
     *
     */
    private function resume($staffId)
    {
        $entry = HrEntryModel::findFirst([
            'conditions' => '  staff_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ]);
        if ($entry) {
            $entry = $entry->toArray();
            $resume = HrResumeModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [
                    'id' => $entry['resume_id'],
                ],
            ]);
            if ($resume) {
                $resume = $resume->toArray();
                return $resume;
            }
        }

        return [];
    }


    /**
     *
     * 是否在职
     *
     * @param $staffId
     *
     */
    private function inService($staffId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ]);
        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];
        if ($staffInfo) {

            return $staffInfo['state'] == 1 ? 1 : 0;
        }
        return 0;
    }



    /**
     * 收到警告书次数
     *
     * @param $staffId
     * @param $month
     */
    private function warningNums($staffId, $month)
    {
        $startDate = $month . '-01';
        $endDate = $month . '-' . date('t', strtotime($month));
        $messageWarnings = MessageWarningModel::find([
            'conditions' => ' staff_info_id = :staff_id: and created_at >= :start_date: and created_at <= :end_date: and is_delete = :is_delete: ',
            'bind' => [
                'staff_id' => $staffId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'is_delete' => 0,
            ]])->toArray();

        return (string) count($messageWarnings);

    }

    /**
     *
     * 考勤不合格天数
     *
     * @param $staffId
     * @param $month
     *
     */
    private function unqualifiedDays($staffId, $month)
    {
        $startDate = $month . '-01';
        $endDate = $month . '-' . date('t', strtotime($month));
        $attendanceDatas = AttendanceDataV2Model::find([
            'conditions' => ' staff_info_id = :staff_id: and  stat_date >= :start_date: and stat_date <= :end_date: and AB != :ab: ',
            'bind' => [
                'staff_id' => $staffId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'ab' => 0,
            ],
        ])->toArray();

        return count($attendanceDatas);

    }

    /**
     * 这个月的旷工天数
     *
     * @param $staffId
     * @param $month
     *
     */
    private function absentDays($staffId, $month)
    {
        $startDate = $month . '-01';
        $endDate = $month . '-' . date('t', strtotime($month));
        $data = AttendanceDataV2Model::findFirst([
            'columns' => ' sum(AB) as sum ',
            'conditions' => ' staff_info_id = :staff_id: and  stat_date >= :start_date: and stat_date <= :end_date: ',
            'bind' => [
                'staff_id' => $staffId,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
        if ($data) {
            $data = $data->toArray();
            return bcdiv((int)$data['sum'], 10, 1);
        } else {
            return (string) 0;
        }

    }

    /**
     * 是否发送警告书
     *
     * @param $staffId 员工工号
     * @param $startDate 开始日期
     * @param $endDate 结束日期
     */
    private function isHasWarningMessage($staffId, $startDate, $endDate)
    {
        return MessageWarningModel::find([
            'conditions' => ' staff_info_id = :staff_id: and created_at >= :start_date: and created_at <= :end_date: and is_delete = :is_delete: ',
            'bind' => [
                'staff_id' => $staffId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'is_delete' => 0,
            ],
        ])->toArray() ? 1 : 0;

    }

    /**
     * 出勤天数
     *
     * @param $staffId
     * @param $month
     *
     */
    private function attendanceDays($staffId, $month)
    {
        $startDate = $month . '-01';
        $endDate = $month . '-' . date('t', strtotime($month));
        $data = AttendanceDataV2Model::findFirst([
            'columns' => ' sum(attendance_time) as sum ',
            'conditions' => ' staff_info_id = :staff_id: and  stat_date >= :start_date: and stat_date <= :end_date: ',
            'bind' => [
                'staff_id' => $staffId,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
        if ($data) {
            $data = $data->toArray();
            return bcdiv((int)$data['sum'], 10, 1);
        } else {

            return  (string) 0;
        }

    }

    /**
     * 入职天数
     *
     * @param $staffId
     */
    private function workDays($staffId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId,
            ],
        ]);
        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];
        if ($staffInfo) {
            $endDate = date('Y-m-d');
            if ($staffInfo['leave_date'] && $staffInfo['state'] == 2) {
                $endDate = $staffInfo['leave_date'];
            }

            $startDate = new DateTime($staffInfo['hire_date']);
            $endDate = new DateTime($endDate);

            return   (string) $startDate->diff($endDate)->days;
        }

        return  (string) 0;
    }

    /**
     * 检查员工当前时间是否在支援期间内
     * @param $staff_id
     * @return bool
     */
    public function checkIsSupportStaff($staff_id,$data = '')
    {
        $isOpen = (new SettingEnvServer())->getSetVal(AttendanceServer::SUB_STAFF_SUPPORT_SWITCH_KEY);
        if($isOpen == AttendanceServer::SUB_STAFF_SUPPORT_CLOSE){
            return false;
        }

        $isInCountry = isCountry('TH') || isCountry('PH') || isCountry('MY');
        if(! $isInCountry){
            return false;
        }
        $att_re = new AttendanceRepository($this->lang, $this->timezone);
        $supportStaffInfo = $att_re->getSupportOsStaffInfo($staff_id,$data);
        if($supportStaffInfo &&  $supportStaffInfo['sub_staff_info_id'] > 0){
            return true;
        } else {
            return false;
        }
    }

    //工具补卡 hcm 马来 不一样 所以搬进来了 这里大部分没动
    public function toolReissueCard($param)
    {
        $add_hour = $this->config->application->add_hour;
        $staff_id = $param['staff_info_id'];
        $date     = $param['attendance_date'];
        $type     = $param['attendance_type'];//1 上班  2 下班 3-全天 全天的去掉了
        $time_tmp = strtotime($param['time']);
        $time     = date('Y-m-d H:i:s', strtotime($param['time']) - $add_hour * 3600);
        $staffRe = new StaffRepository($this->lang);
        $staffInfo = $staffRe->getStaffPosition($staff_id);
        //判断是否存在 记录 和 补申请待审核状态记录 如果存在 不允许 候补
        $audit_model    = new AuditRepository($this->lang);
        $pa['day']      = date('Y-m-d', strtotime($date));
        $pa['staff_id'] = $staff_id;

        $att_model = new AttendanceRepository($this->lang, $this->timezone);

        $att_info = $att_model->getDateInfo($staff_id, $pa['day']);
        if (!empty($att_info)) {//真实打卡空 判断补卡
            if ($type == StaffWorkAttendanceModel::ATTENDANCE_ON) {
                if (!empty($att_info['started_at'])) {
                    throw new ValidationException('have been exist');
                }
                if (!empty($att_info['end_at']) && strtotime($att_info['end_at']) < $time_tmp) {
                    throw new ValidationException('start_time must earlier than end_time');
                }
            }
            if ($type == StaffWorkAttendanceModel::ATTENDANCE_OFF) {
                if (!empty($att_info['end_at'])) {
                    throw new ValidationException('have been exist');
                }
                if (!empty($att_info['started_at']) && strtotime($att_info['started_at']) > $time_tmp) {
                    throw new ValidationException('start_time must earlier than end_time');
                }
            }
        }

        //新增 补卡时间不能大于当前时间
        if($time_tmp > time()){
            throw new ValidationException($this->getTranslation()->_('1008'));
        }

        //获取补卡记录
        $apply_info = $audit_model->get_attendance_info($staff_id, $pa['day'], $type);
        if (!empty($apply_info)) {
            throw new ValidationException('have been applied');
        }

        //泰国个人代理申请补卡 如果是下班补卡 验证java 接口
        $icFlag = isCountry('TH') && in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether) && $type == StaffAuditModel::ATTENDANCE_TYPE_FIRST_LOW;
        if($icFlag){
            $checkServer = new CheckPunchOutServer($this->lang, $this->timezone);
            $parcelInfo = $checkServer->check_ic_from_java($staff_id,$date);
            if(!empty($parcelInfo['num']) || !empty($parcelInfo['abnormalNum'])){
                throw new ValidationException($this->getTranslation()->_('ic_reissue_notice'));
            }
        }

        //新增 出差期间 打卡审批 逻辑判断 https://l8bx01gcjr.feishu.cn/docs/doccnyA3C6x0DsUA3ZGlztvhDLf#  补卡 又不判断 出差打卡了 春华 注释掉了
        //新需求 回写 班次到打卡表 徐华伟
        //新逻辑 班次数据源 变更为 固化班次表 hr_staff_transfer
        $shiftServer = new HrShiftServer();
        $shift_data = $shiftServer->getShiftInfos($staff_id,[$date]);
        $this->logger->write_log(['shift_data'=>$shift_data,'param'=>$param,'toolReissueCard'], 'info');

        $auditRe = new AuditRepository($this->lang);
        [$organization_id, $organization_type] = $auditRe->getOrganization($staff_id, $date);
        //work day
        $insert['working_day'] = $staffRe->get_is_working_day($staffInfo,$date);

        //组装数据
        $insert['staff_info_id']     = $staff_id;
        $insert['attendance_date']   = $date;
        $insert['shift_start']       = $shift_data[$date]['start'] ?? '';
        $insert['shift_end']         = $shift_data[$date]['end'] ?? '';
        $insert['shift_id']          = $shift_data[$date]['shift_id'] ?? 0;
        $insert['organization_id']   = $organization_id;
        $insert['organization_type'] = $organization_type;

        if ($type == StaffWorkAttendanceModel::ATTENDANCE_ON) {//上班
            $insert['started_at']     = $time;
            $insert['started_state']  = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY;
            $insert['started_remark'] = 'bi_system';
        }

        if ($type == StaffWorkAttendanceModel::ATTENDANCE_OFF) {//下班补卡
            $insert['end_at']     = $time;
            $insert['end_state']  = StaffWorkAttendanceModel::STATE_MAKE_UP_CARD_SYSTEM_ENTRY;
            $insert['end_remark'] = 'bi_system';
        }
        if (empty($att_info)) {//insert
            $flag = $att_model->insertInfo($insert);
        } else {//update
            if (!empty($att_info['shift_start'])) {//如果有班次信息 不再更新班次
                unset($insert['shift_start']);
                unset($insert['shift_end']);
                unset($insert['shift_id']);
                unset($insert['working_day']);
            }
            $flag = $att_model->updateInfo($att_info['id'], $insert);
        }
        $attendancePenaltyServer = new AttendancePenaltyServer($this->lang, $this->timezone);

        if (isCountry('PH')) {
            //HCM补卡
            $attendancePenaltyServer->push($staff_id, $date, BasePenaltyServer::SRC_MAKE_UP);
        }

        if (isCountry('TH')) {
            //https://flashexpress.feishu.cn/docx/Tcw8dzXraoWFqhxJvEIcIxqznxb
            //补卡同步bi 重算处罚数据
            $sync_server = new SyncServer($this->lang, $this->timezone);
            $params      = [
                'staff_id'     => $staff_id,
                'recheck_date' => $date,
                'type'         => $type,
            ];
            $sync_server->sync_fbi_attendance($params, 'abnormal.staff_clock_in_reset');
        }

        $cardData['staff_id']        = $staff_id;
        $cardData['attendance_date'] = $date;
        $cardData['shift_type']      = empty($att_info) ? StaffWorkAttendanceModel::SHIFT_TYPE_ONLY : $att_info['shift_type'];
        $cardData['started_at']      = !empty($insert['started_at']) ? $insert['started_at'] : NULL;
        $cardData['end_at']          = !empty($insert['end_at']) ? $insert['end_at'] : NULL;
        //向FBI 生产 补卡消息-处罚
        $attendancePenaltyServer->sendCardReplacementMsg($cardData);
        //个人代理 工具补下班卡 通知重算
        if($icFlag){
            $attendanceServer = new AttendanceServer($this->lang, $this->timezone);
            $attendanceServer->sendIcOffCard($staff_id,$date,$param['time']);
        }

        if ($flag) {
            return $this->checkReturn([]);
        }
        return $this->checkReturn(-3, 'hcm reissue insert error');
    }

    /**
     * 主账号，外勤打卡，给上级发消息+邮件
     * @param $data
     * @return bool
     */
    public function sendFieldPersonnelMessage($data)
    {
        $redis = $this->getDI()->get('redisLib');
        $field_personnel_list_key = self::REDIS_FIELD_PERSONNEL_MESSAGE_LIST;

        $content = json_encode($data, JSON_UNESCAPED_UNICODE);
        $redis->lpush($field_personnel_list_key, $content);

        return true;
    }

    /**
     * @description:获取出差目的地国家的打卡时区
     * @return:$time_zone
     * @author: L.J
     * @time: 2023/1/10 20:14
     */
    public function getBusinessAttendanceTimeZone($param)
    {
        $business_trip_type  = $param['business_trip_type'];   //出差类型
        $destination_country = $param['destination_country'];  //出差目的地国家
        $click_lat           = $param['click_lat'];//维度
        $click_lng           = $param['click_lng'];// 经度
        $staff_info_id       = $param['staff_info_id'];


        $time_zone               = $this->config->application->add_hour;
        $destination_country_all = BusinessTripModel::DESTINATION_COUNTRY; //各国国家的时区

        $is_time_zone            = $destination_country_all[$destination_country]['is_time_zone'] ?? 0;  //是否需要调整时区
        $country_time_zone       = $destination_country_all[$destination_country]['time_zone'] ?? $time_zone;     //时区 如果没有就是当地时区
        //如果出差国家 == 境外出差,并且需要调整时区
        if ($business_trip_type == BusinessTripModel::BTY_FOREIGN && !empty($is_time_zone)) {
            //根据经纬度获取时区
            $get_maps_time_zone = $this->getMapsTimeZone($click_lat, $click_lng,$staff_info_id);
            //判断时区是否和出差目的地国家时区吻合
            if ($country_time_zone == $get_maps_time_zone) {
                $time_zone = $country_time_zone;
            }
            $this->logger->write_log("getBusinessAttendanceTimeZone country_time_zone =>{$country_time_zone}  get_maps_time_zone => {$get_maps_time_zone}  time_zone => {$time_zone} param=>".json_encode($param,
                    JSON_UNESCAPED_UNICODE), 'info');
        }
        return $time_zone;
    }

    /**
     * @description:根据维度,经度获取时区
     * @return:$time_zone
     * @author: L.J
     * @time: 2023/1/10 21:18
     */
    public function getMapsTimeZone($lat, $lng,$staff_info_id)
    {
        //https://developers.google.com/maps/documentation/timezone/get-started?hl=zh-cn#maps_http_timezone-py
        //获取 谷歌 Time Zone API 的  url
        $time_zone_api = (new SettingEnvServer())->getSetValFromCache('google_maps_time_zone_api_url');

        $timestamp = time();
        // 这里的秘钥不要记录了
        $google_maps_key = env('google_maps_api_key', '');

        $url       = $time_zone_api."?location={$lat}%2C{$lng}&timestamp={$timestamp}&key=";
        $res       = HttpCurl::httpGet($url.$google_maps_key);
        $this->logger->write_log("getMapsTimeZone staff_info_id : {$staff_info_id} , curl : {$url} , res : {$res}", 'info');

        $res = json_decode($res, true);
        if (!isset($res['rawOffset'])) {
            //如果异常  返回本国时区
            $this->logger->write_log("getMapsTimeZone fail staff_info_id : {$staff_info_id} , curl: {$url} , res {$res}");
            return $this->config->application->add_hour;
        }

        //解析时区
        return floor($res['rawOffset'] / 3600);
    }

    /**
     * @description 发送稽查到疑似作弊消息给网点主管
     * @param array $data 稽查参数
     *
     *
     *
     * @return void
     */
    public function sendDetectedMessage($data)
    {
        $storeId      = $data['store_id'] ?? "";         //所属网点
        $staffId      = $data['staff_info_id'] ?? "";    //被识别出作弊的员工ID
        $detectedAt   = $data['detected_at'] ?? "";      //当前时间
        $source       = $data['source'] ?? "";           //来源
        $staffInfoArr = [];

        if (empty($storeId) || empty($staffId) || empty($detectedAt)) {
            $this->logger->write_log("[sendDetectedMessage err] params err" . json_encode($data), "notice");
            return;
        }

        //查找MY Resource部门、  Resource Officer、Resource Specialist、Resource Supervisor职位的
        //在职、在编员工
        if (isCountry('MY')) {
            $staffInfo = HrStaffInfoModel::find([
                "node_department_id = :department_id: and job_title IN({job_title_ids:array}) and state = 1 and formal = 1 and is_sub_staff = 0",
                "bind" => [
                    "department_id" => 15085,
                    "job_title_ids" => [
                        enums::$job_title['resource_officer'],
                        enums::$job_title['resource_specialist'],
                        enums::$job_title['resource_supervisor'],
                    ],
                ],
                "columns" => "staff_info_id",
            ])->toArray();
            $staffInfoArr = array_column($staffInfo, 'staff_info_id');
            if (empty($staffInfoArr)) {
                $this->logger->write_log("[sendDetectedMessage err] staff_info is empty" . json_encode($data), "info");
            }
        }
        $staffInfo = (new StaffRepository())->getStaffInfoOne($staffId, 'name as staff_name,formal,hire_type,sys_store_id,manger');

        $storeInfo = SysStoreModel::findFirst([
            "id = :store_id:",
            "bind" => [
                "store_id" => $storeId,
            ],
        ]);
        $storeName = empty($storeInfo) ? "": $storeInfo->name;

        //泰国个人代理
        if ((isCountry('TH') || isCountry('PH')) && $source == AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT) {
            $staffInfoArr[] = !empty($staffInfo['manger']) ? $staffInfo['manger'] : '';
        } else if ((isCountry('TH') || isCountry('PH')) && in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $branchSupervisor = HrStaffInfoModel::findFirst([
                'conditions' => 'sys_store_id = :sys_store_id: and state = 1 and formal = 1 and is_sub_staff = 0 and job_title = :job_title:',
                'bind' => [
                    'sys_store_id' => $staffInfo['sys_store_id'],
                    'job_title'    => enums::$job_title['branch_supervisor'],
                ],
            ]);
            $staffInfoArr[] = !empty($branchSupervisor) ? $branchSupervisor->staff_info_id : '';
        }

        $publicRepo = new PublicRepository();
        $staffLang = (new StaffServer())->getBatchStaffLanguage($staffInfoArr);
        foreach ($staffInfoArr as $staff_info_id) {
            if(empty($staff_info_id)){
                continue;
            }
            $lang = $staffLang[$staff_info_id] ?? "en";
            $t = $this->getTranslation($lang);

            if ($staffInfo['formal'] == HrStaffInfoModel::FORMAL_0) { //外协
                $title = $t->_('punch_check_detected_message', [
                    'store_name' => $storeName,
                ]);
                $content = $t->_('punch_check_detected_content', [
                    'store_name' => $storeName,
                    'staff_info_id' => $staffId,
                    'detected_at' => $detectedAt,
                ]);
            } else if ($source == AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT) { //保存底片时检测到作弊
                $title = $t->_('punch_check_agent_attachment_detected_message', [
                    'staff_info_id' => $staffId,
                ]);
                $content = $t->_('punch_check_agent_attachment_detected_content', [
                    'staff_info_id' => $staffId,
                    'staff_name' => $staffInfo['staff_name'],
                ]);
            } else if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {

                //打卡检测作弊时、2小时作弊检测时（人脸不匹配后，检测到作弊）
                if (in_array($source, [AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_ATTENDANCE,
                    AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE_DETECT_CHEATING])) {
                    $title = $t->_('punch_check_agent_detected_message', [
                        'staff_info_id' => $staffId,
                    ]);
                    $content = $t->_('punch_check_agent_detected_content', [
                        'staff_info_id' => $staffId,
                    ]);
                } else { //2小时检测人脸与底片不符
                    $title = $t->_('punch_check_agent_cycle_detected_message', [
                        'staff_info_id' => $staffId,
                    ]);
                    $content = $t->_('punch_check_agent_cycle_detected_content', [
                        'staff_info_id' => $staffId,
                    ]);
                }
            } else {
                $title = $content = '';
            }

            if (empty($title) && $content) {
                continue;
            }
            $param = [
                'staff_info_id'   => $staff_info_id,
                'message_title'   => $title,
                'message_content' => $content,
                'type'            => MessageEnums::CATEGORY_GENERAL,
            ];
            $publicRepo->sendMessageToSubmitter($param);
        }
    }

    /**
     * @description 获取发送邮件列表
     * @return array|string[]
     */
    public function getEmailList()
    {
        $model = new \FlashExpress\bi\App\Server\SettingEnvServer();
        $email = $model->getSetVal('outsourcing_attendance_punch_card_email');
        $emailList = !empty($email) ? explode(',', $email) : [];

        return $emailList;
    }


    /**
     * @description:FMS司机详情展示人脸底片
     * @param null
     * @return:
     * @author: L.J
     * @time: 2023/2/15 10:59
     */
    public function getStaffAttendanceNegative($param = [])
    {
        $return_data = ['code' => ErrCode::SUCCESS, 'data' => [], 'msg' => 'success'];
        //工号
        $staff_info_ids = array_values($param['staff_info_ids'] ?? []);
        if (empty($staff_info_ids) || count($staff_info_ids) > 500) {
            $return_data['code'] = ErrCode::ERROR;
            $return_data['msg']  = 'staff_info_ids in 1~500 between quantity';
            return $return_data;
        }
        $list = HrStaffInfoModel::find([
            'conditions' => ' staff_info_id in ({staff_info_ids:array}) ',
            'bind'       => ['staff_info_ids' => $staff_info_ids],
            'columns'    => 'staff_info_id,hire_date,created_at,leave_date',
        ])->toArray();
        if (empty($list)) {
            return $return_data;
        }
        //获取打卡图片
        $face_img_list = StaffWorkAttendanceAttachmentModel::find([
            'conditions' => ' staff_info_id in ({staff_info_ids:array})',
            'bind'       => ['staff_info_ids' => $staff_info_ids],
            'columns'    => 'staff_info_id,work_attendance_bucket,work_attendance_path',
        ])->toArray();
        $face_img_list = array_column($face_img_list, null, 'staff_info_id');
        //拼接外网地址
        $server_point = (new SettingEnvServer())->getSetValFromCache('server_point');
        $add_hour = $this->config->application->add_hour;
        foreach ($list as &$v) {
            $v['created_at'] = (string)(!empty($v['created_at']) ? date('Y-m-d H:i:s',
                strtotime($v['created_at']) + ($add_hour * 3600)) : '');
            $v['hire_date']  = (string)(!empty($v['hire_date']) ? date('Y-m-d', strtotime($v['hire_date'])) : '');
            $v['leave_date'] = (string)(!empty($v['leave_date']) ? date('Y-m-d', strtotime($v['leave_date'])) : '');
            $v['negative']   = (string)(!isset($face_img_list[$v['staff_info_id']]) ? '' : 'https://'.$face_img_list[$v['staff_info_id']]['work_attendance_bucket'].$server_point.$face_img_list[$v['staff_info_id']]['work_attendance_path']);
        }

        $return_data['data'] = $list;
        return $this->checkReturn($return_data);
    }

    public function sendClockToMs($clockData)
    {
        if (isCountry(['VN', 'ID'])) {
            return true;
        }
        $mq = new RocketMQ('staff_clock');
        $mq->setType(RocketMQ::TAG_NAME_STAFF_CLOCK);
        return $mq->sendMsgByTag($clockData);
    }

    /**
     * @description 检验员工需要刷脸
     * @param $staff_info_id
     * @return array
     */
    public function getBrushFaceStaffInfo($staff_info_id, $attendance_date = '')
    {
        $result = ['staff_info' => [], 'is_check' => false];
        if (empty($staff_info_id)) {
            return $result;
        }
        if(empty($attendance_date)){
            $attendance_date = date('Y-m-d');
        }
        //校验是否为快递员、外协人员
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $staff_info_id,
            ],
            'columns'    => 'job_title,formal,sys_store_id,hire_type',
        ]);
        if (empty($staffInfo)) {
            return $result;
        }
        $result['staff_info'] = $staffInfo->toArray();
        //其他国家不调用
        if (isCountry('LA') || isCountry('VN') || isCountry('ID')) {
            return $result;
        }

        //马来 只有外协
        if (isCountry('MY')) {
            if ($staffInfo->formal != enums::$staff_formal['os']) {
                return $result;
            }
        }

        //菲律宾 泰国 外协 个人代理
        if (isCountry('PH') || isCountry('TH')) {
            // TH既不是外协、也不是个人代理不检测作弊
            if ($staffInfo->formal != enums::$staff_formal['os'] && !in_array($staffInfo->hire_type,
                    HrStaffInfoModel::$agentTypeTogether)) {
                return $result;
            }
        }
        //三国 外协
        if ($staffInfo->formal == enums::$staff_formal['os']) {
            //众包外协不需要获取打卡信息,直接按照间隔时间来算
            if ($staffInfo->hire_type == HrStaffInfoModel::HIRE_TYPE_12) {
                $result['is_check'] = true;
                return $result;
            }
            $jobTitle = $this->getCourierJobTitle();
            if (!in_array($staffInfo->job_title, $jobTitle)) {
                //非快递员，无需校验
                return $result;
            }
        }
        //获取打卡信息
        $punchInfo = StaffWorkAttendanceModel::findFirst([
            'staff_info_id = :staff_info_id: and attendance_date = :attendance_date:',
            'bind' => [
                'staff_info_id'   => $staff_info_id,
                'attendance_date' => $attendance_date,
            ],
        ]);
        if (empty($punchInfo) || !empty($punchInfo->end_at)) { //没打卡或者打了下班卡
            //无需校验
            return $result;
        }

        //个人代理
        if (isCountry('TH') || isCountry('PH')) {
            if (in_array($staffInfo->hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                $configs     = (new SettingEnvServer())->listByCode([
                    'backyard_check_position',
                    'backyard_check_face_switch',
                ]);
                $configs     = array_column($configs, 'set_val', 'code');
                $jobTitleIds = !empty($configs['backyard_check_position']) ? explode(',', $configs['backyard_check_position']) : [];

                if (!in_array($staffInfo->job_title, $jobTitleIds) ||
                    $configs['backyard_check_face_switch'] == CommonEnums::SWITCH_CLOSED
                ) {
                    //无需校验
                    return $result;
                }
            }
        }
        $result['is_check'] = true;
        return $result;
    }

    /**
     * @description:获取外协对比图片
     * @param array $paramIn
     * @return string :
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/2/16 15:52
     */
    public function getOutsourcingFaceImg($paramIn = [])
    {
        $staff_info_id = $paramIn['staff_info_id'];
        $hire_type     = $paramIn['hire_type'];
        $flag          = $paramIn['flag'] ?? 0;
        //获取底片
        $faceImageUrl = '';
        //众包
        if ($hire_type == HrStaffInfoModel::HIRE_TYPE_12) {
            $fms_http_url = (new SettingEnvServer())->getSetValFromCache('fms_http_url');
            $url          = $fms_http_url . '/svc/face/pic';
            $http_result  = $this->httpPost($url, ['external_staff_id' => $staff_info_id]);
            $faceImageUrl = $http_result['data']['face_pic_url'] ?? '';
        } else {
            //非众包
            $faceImageUrl = $this->get_face_img($staff_info_id, $flag ?? 0);
        }

        if (empty($faceImageUrl)) {
            //不存在底片  众包有可能获取不到底片   不影响后续流程   联系 fleet
            $this->logger->write_log("getOutsourcingFaceImg hire_type=>{$hire_type}  staff_info_id=> {$staff_info_id} url=>{$faceImageUrl} ",
                'info');
        }
        return $faceImageUrl;
    }

    /**
     * @description:保存外协刷脸校验信息
     * @throws ValidationException
     * @author: L.J
     * @time: 2023/2/16 15:44
     */
    public function addStaffWorkDetectFaceRecord($paramIn = [])
    {
        $organization_id           = $paramIn['organization_id'];
        $staff_info_id             = $paramIn['staff_info_id'];
        $score_arr                 = $paramIn['score_arr'];
        $sourceUrl                 = $paramIn['source_url'];
        $os_submit_face_image_path = $paramIn['os_submit_face_image_path'];
        $os_face_image_source_path = $paramIn['os_face_image_source_path'];
        $type                      = $paramIn['type'];
        $live_score                = $paramIn['live_score'] ?? null;
        $attendance_date           = $paramIn['attendance_date'];

        //保存比对结果
        //保存外协提交的人脸照片 + 外协人人员的底片
        $model = new StaffWorkDetectFaceRecordModel();
        $model->setStaffInfoId($staff_info_id);
        $model->setOrganizationId($organization_id);
        $model->setLiveScore($live_score);
        $model->setAttendanceDate($attendance_date);
        $model->setOsSubmitFaceImagePath($os_submit_face_image_path);
        $model->setOsFaceImageSourcePath($os_face_image_source_path);
        $model->setType($type);
        $model->setCreatedAt(date('Y-m-d H:i:s'));

        if ($score_arr['http_code'] == 200 && $score_arr['score'] < $this->score) {//不匹配

            $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_DETECTED);
            $model->save();
            $this->logger->write_log("StaffWorkDetectFaceRecordModel {$staff_info_id} {$organization_id}", 'info');

            if ($type == StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE) {
                //检测到您已有正式员工账号，请勿使用外协/个人代理账号操作
                $params = [
                    'store_id'      => $organization_id, //所属网点
                    'staff_info_id' => $staff_info_id,                                       //被识别出作弊的员工ID
                    'detected_at'   => date("Y-m-d H:i:s"),                           //当前时间
                    'source'        => AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE,      //来源
                ];
                //发送消息
                $redis = $this->getDI()->get('redisLib');
                $content = json_encode($params, JSON_UNESCAPED_UNICODE);

                $this->logger->write_log("[image_verify_cyclicalAction lpush]" . json_encode($content, JSON_UNESCAPED_UNICODE), 'info');
                $redis->lpush(AttendanceServer::REDIS_SEARCH_STAFF_FACE_MESSAGE_KEY, $content);

                $detectParams = [
                    'staff_info_id' => $staff_info_id,
                    'click_url'     => $sourceUrl,
                    'source'        => AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE,
                    'store_id'      => $organization_id,
                ];
                $this->detectAgentCheating($detectParams);
            }

            //比对不匹配只记录，不报错
            throw new ValidationException('success', ErrCode::SUCCESS);
        } else {
            $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_NOT_MATCH);
            $model->save();
            $this->logger->write_log("StaffWorkDetectFaceRecordModel {$staff_info_id} {$organization_id}",
                'info');
        }
        return true;
    }

    /**
     * 人脸图片上传
     * @param $staff_id
     * @param $filename
     * @return void
     * @throws Exception
     */
    public function faceFormatUrlFle($staff_id,$filename){

        $url = env('api_img_upload');
        if(empty($url)){
            throw new Exception(' env api_img_upload empty!');
        }
        if (empty($filename)) {
            throw new ValidationException('parameter error need fileName');
        }

        //客户端安卓 要传带时间戳的图片 下面需要兼容
        $type = '';
        if (strstr($filename, 'source')) {
            $type = 'WORK_ATTENDANCE_SOURCE';
        }

        if (strstr($filename, 'duty')) {
            $type = 'WORK_ATTENDANCE_TMP';
        }
        if (empty($type)) {
            throw new BusinessException('parameter error need correct fileName');
        }
        $this->logger->write_log("format_url_fle_{$staff_id} {$type} {$url}",'info');
        $fle_rpc = (new ApiClient($url,'','buildPutObjectParams', $this->lang));
        $fle_rpc->setParam([$type, $filename, '']);
        return $fle_rpc->execute();
    }



    /**
     * 人脸图片上传
     * @param $staff_id
     * @param $filename
     * @return void
     * @throws Exception
     * @throws BusinessException|ValidationException
     */
    public function faceFormatUrlHcm($staff_id, $filename)
    {
        if (empty($filename)) {
            throw new ValidationException('parameter error need fileName');
        }
        //客户端安卓 要传带时间戳的图片 下面需要兼容
        $type = '';
        if (strstr($filename, 'source')) {
            $type = 'WORK_ATTENDANCE_SOURCE';
        }

        if (strstr($filename, 'duty')) {
            $type = 'WORK_ATTENDANCE_TMP';
        }
        if (empty($type)) {
            throw new BusinessException('parameter error need correct fileName');
        }
        $this->logger->write_log("format_url_fle_{$staff_id} {$type} ", 'info');
        $fle_rpc = (new ApiClient('hcm_rpc', '', 'buildPutObjectParams', $this->lang));
        $fle_rpc->setParams(['biz_type' => $type, 'filename' => $filename]);
        return $fle_rpc->execute();
    }


    //根据时间判断 在那个区间
    public function liveHostShift($hour = null)
    {
        if (is_null($hour)) {
            return '';
        }
        $settingHour = (new SettingEnvServer())->listByCode(['day_shift_duration', 'night_shift_duration']);
        if (!empty($settingHour)) {
            $settingHour = array_column($settingHour, 'set_val', 'code');
        }
        $dayHour   = empty($settingHour['day_shift_duration']) ? 9 : $settingHour['day_shift_duration'];
        $nightHour = empty($settingHour['night_shift_duration']) ? 6 : $settingHour['night_shift_duration'];
        //4-16点 白班
        if ($hour >= 4 && $hour < 16) {
            return $this->getTranslation()->_('live_day_shift', ['hour' => $dayHour]);
        } else {
            return $this->getTranslation()->_('live_night_shift', ['hour' => $nightHour]);
        }
    }

    /**
     * 是否属于客服部门
     * @param $staffInfo
     * @return bool
     */
    public function isBelongCustomer($staffInfo){
        //各个国家客服部门id 泰国 id 3 马来 15008 菲律宾 5426
        $departmentId = (new SettingEnvServer())->getSetVal('department_customer_id');
        if(empty($departmentId)){
            return false;
        }
        $departmentId = intval($departmentId);
        //获取 所有子部门id
        $allIds = (new SysDepartmentServer($this->lang, $this->timezone))->getDepartmentIds($departmentId);
        if(in_array($staffInfo['sys_department_id'], $allIds) || in_array($staffInfo['node_department_id'], $allIds)){
            return true;
        }
        return false;
    }

    //下班打卡 同步客服系统
    public function syncWebHookCustomer($staffInfo, $param)
    {
        $redis                   = $this->getDI()->get('redisLib');
        $key                     = self::SYNC_CUSTOMER_KEY;
        $country_code            = $this->config->application->country_code;
        $syncData['type']        = $param['attendance_category'];
        $syncData['country']     = strtoupper($country_code);
        $syncData['staffId']     = $staffInfo['staff_info_id'];
        $syncData['staffName']   = $staffInfo['name'];
        $syncData['recodeDate']  = $param['attendance_date'];
        $syncData['operateTime'] = time();

        $this->logger->write_log("sync_customer data ".json_encode($syncData), 'info');
        $redis->lpush($key, json_encode($syncData));
        return true;
    }


    //上班打卡 发病假消息 只发一次
    public function sickMessage($staffInfo, $param)
    {
        $redis                   = $this->getDI()->get('redisLib');
        $key                     = self::SICK_MESSAGE;
        $data['type']            = $param['attendance_category'];
        $data['staff_info_id']   = $staffInfo['staff_info_id'];
        $data['attendance_date'] = $param['attendance_date'];
        $data['lang']            = $this->lang;

        $this->logger->write_log("send_sick_msg data ".json_encode($data), 'info');
        $redis->lpush($key, json_encode($data));
        return true;
    }

    /**
     * V21322【TH|BY|消息】 外协仓管自动发送合同
     * @param array $staffInfo 打卡员工信息组
     * @return bool
     */
    public function sendOsStaffContract($staffInfo)
    {
        //非编制（外协）-不做任何处理
        if ($staffInfo['formal'] != HrStaffInfoModel::FORMAL_0) {
            return true;
        }

        $redis                   = $this->getDI()->get('redisLib');
        $key                     = self::REDIS_SEND_OS_STAFF_CONTRACT_KEY;
        $data['staff_info_id']   = $staffInfo['staff_info_id'];
        $data['lang']            = $this->lang;

        $this->logger->write_log('sendOsStaffContract data ' . json_encode($data), 'info');
        $redis->lpush($key, json_encode($data));
        return true;
    }


    /**
     * 检测外协作弊
     * @param array $params
     * @return void
     * @throws ValidationException
     */
    public function detectOutSourceStaffCheating($params = [])
    {
        $staffInfoId = $params['staff_info_id'];
        $clickUrl    = $params['click_url'];
        $userInfo    = $params['user_info'];
        $source      = $params['source'];

        //是否入职后第一次打卡
        $staffAttendanceImage = StaffWorkAttendanceAttachmentModel::findFirstByStaffInfoId($staffInfoId);
        if (empty($staffAttendanceImage)) {
            $groupIdList = $this->getRegionList();
        } else {
            $groupIdList = $this->getRegionList($userInfo['organization_id']);
            if (RUNTIME == 'dev') { //测试环境只有1大区数据
                $groupIdList = ['1'];
            }
        }

        //全网/所属大区检测作弊
        $params = [
            'source_url'    => $clickUrl,
            'store_id'      => $userInfo['organization_id'],
            'staff_id'      => $staffInfoId,
            'group_id_list' => $groupIdList,
            'source'        => $source,
        ];
        $this->searchRepeatFace($params);
    }

    /**
     * 检测外协作弊
     * @param array $params
     * @return void
     */
    public function detectAgentCheating($params = [])
    {
        $staffInfoId        = $params['staff_info_id'];
        $clickUrl           = $params['click_url'];
        $source             = $params['source'];
        $storeId            = $params['store_id'];

        //系统自动发送给员工 发送消息调用接口
        $data = [
            'click_url'     => $clickUrl,
            'staff_info_id' => $staffInfoId,
            'source'        => $source, //来源 1=打卡 2=定时检测
            'store_id'      => $storeId, //员工所属网点
        ];
        $rmq  = new RocketMQ('cheating_detect');
        $rmq->setType(RocketMQ::TAG_DETECT_CHEATING);
        $rid = $rmq->sendMsgByTag($data);

        $this->logger->write_log([
            'function'      => 'detectAgentCheating',
            'rid'           => $rid,
            'click_url'     => $clickUrl,
            'staff_info_id' => $staffInfoId,
            'source'        => $source,
            'store_id'      => $storeId,
        ], 'info');
    }

    /**
     * 检测外协作弊
     * @param array $params
     * @return bool
     * @throws ValidationException
     */
    public function doDetectAgentCheating($params = [])
    {
        $this->logger->write_log(sprintf('doDetectAgentCheating params:%s', json_encode($params)), 'info');

        $staffInfoId        = $params['staff_info_id'];
        $clickUrl           = $params['click_url'];
        $storeId            = $params['store_id'];
        $source             = $params['source'];

        $groupIdList = $this->getRegionList($storeId);
        $params = [
            'click_url'        => $clickUrl,
            'store_id'         => $storeId,
            'staff_info_id'    => $staffInfoId,
            'group_id_list'    => $groupIdList,
            'source'           => $source,
        ];
        $res = $this->searchAgentRepeatFace($params);

        return !empty($res);
    }

    /**
     * 检测作弊
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    private function searchAgentRepeatFace($params)
    {
        $imageUrl    = $params['click_url'];
        $staffInfoId = $params['staff_info_id'];
        $source      = $params['source'];
        $countryCode = $this->getCountryCode($source);
        $staffServer = new StaffServer();

        //获取大区
        $staffInfo   = $staffServer->getStaffInfo(['staff_info_id' => $staffInfoId]);
        $groupIdList = $this->getRegionList();

        //拼接图片地址
        $format['bucket'] = $this->config->application->oss_bucket;
        $format['path']   = $imageUrl;
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $url = $this->format_oss($format, $flag ?? 0);

        //生成唯一ID
        $nodeProvider = new RandomNodeProvider();
        $uuid = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();

        $params = [
            "url"           => $url,
            "request_id"    => $uuid,
            "country"       => $countryCode,
            "group_id_list" => $groupIdList,
        ];
        $result = AiServer::getInstance()->setConfig(enums::IDENTIFY_ANALYZE_SEARCH_FACE)->sendEx(json_encode($params), $staffInfoId);
        if (empty($result)) {
            $this->logger->write_log(sprintf("search_face error:{$url}, params:%s", json_encode($params)), 'info');
            return false;
        }
        //获取考勤日期
        $staff_model           = new StaffRepository($this->lang);
        $info                  = $staff_model->login_info($staffInfoId);
        $info['staff_info_id'] = $staffInfoId;
        $param['user_info']    = $info;
        $attServer             = Tools::reBuildCountryInstance($this, [$this->lang, $this->timezone]);
        if (isCountry('MY')) {
            $attendanceInfo = $attServer->newAttendanceInfo($param);
        } else {
            $attendanceInfo = $attServer->attendance_info($param);
        }
        $attendanceDate = $attendanceInfo['attendance_date'] ?? date('Y-m-d');

        $model = new StaffWorkDetectFaceRecordModel();
        $model->setStaffInfoId($staffInfoId);
        $model->setOrganizationId($staffInfo['sys_store_id']);
        //日期要用考勤日期
        $model->setAttendanceDate($attendanceDate);
        $model->setOsSubmitFaceImagePath($this->format_oss($format));
        $model->setCreatedAt(date("Y-m-d H:i:s"));

        //需要区分个人代理打卡/保存底片
        if ($source == AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT) {
            $type = StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_COURIER_ATTACHMENT;
        } else if ($source == AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE) {
            $type = StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT_CYCLE;
        } else {
            $type = StaffWorkDetectFaceRecordModel::DETECT_FACE_RECORD_TYPE_AGENT;
        }
        $model->setType($type);

        //搜索到人脸时
        //score字段大于等于93分，flash_hr_exist = true
        if (isset($result['result']['flash_hr_exist']) && $result['result']['flash_hr_exist']) { //发现比对的问题

            //获取匹配到的员工底片
            $matchStaffInfoId   = $result['result']['person_id'];
            $matchStaffImageUrl = $this->get_face_img($matchStaffInfoId);
            if ($this->isValidMatch($matchStaffInfoId, $staffInfoId) === false) {

                $model->setWorkAttendancePath($matchStaffImageUrl);
                $model->setMatchStaffInfoId($matchStaffInfoId);
                $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_NOT_MATCH);
                $model->save();
                return true;
            }

            //save 员工提交的照片 + 匹配到的正式员工的底片
            $model->setWorkAttendancePath($matchStaffImageUrl);
            $model->setMatchStaffInfoId($matchStaffInfoId);
            $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_HAS_MATCH);
            $model->save();

            //检测到您已有正式员工账号，请勿使用外协/个人代理账号操作
            $message = [
                'store_id'      => $staffInfo['sys_store_id'],                    //所属网点
                'staff_info_id' => $staffInfoId,                                  //被识别出作弊的员工ID
                'detected_at'   => date("Y-m-d H:i:s"),                    //当前时间
                'source'        => $source == AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE  //来源
                    ? AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE_DETECT_CHEATING
                    : $source,
            ];
            //发送消息
            $redis   = $this->getDI()->get('redisLib');
            $content = json_encode($message, JSON_UNESCAPED_UNICODE);
            $this->logger->write_log("[searchRepeatFace lpush]" . json_encode($content, JSON_UNESCAPED_UNICODE),
                'info');
            $redis->lpush(self::REDIS_SEARCH_STAFF_FACE_MESSAGE_KEY, $content);
            $this->logger->write_log("StaffWorkDetectFaceRecord {$staffInfoId} {$staffInfo['sys_store_id']}, state = 1",
                "info");
        } else {
            $model->setState(StaffWorkDetectFaceRecordModel::MATCH_STATE_NOT_MATCH);
            $model->save();
            $this->logger->write_log("StaffWorkDetectFaceRecordModel {$staffInfoId} {$staffInfo['sys_store_id']}, state = 0", "info");
        }
        return $result;
    }

    /**
     * 获取 ai 人脸库类型
     * @param $source
     * @return string
     */
    public function getCountryCode($source): string
    {
        switch ($source) {
            //老库全部快递员人脸
            //对应Task (向 AI 同步快递员人脸 attendance generateStaffInfo)
            case AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_SAVE_ATTACHMENT:
                $countryCode = strtolower(env('country_code', 'Th'));
                break;
            //新库全部正式快递员人脸
            //对应Task (向 AI 同步正式快递员人脸 attendance generateFullTimeStaffInfo)
            case AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_ATTENDANCE:
            case AttendanceEnums::CHECK_REPEAT_FACE_SOURCE_CYCLE:
            default:
                $countryCode = strtolower(env('country_code', 'Th')) . '-formal';
                break;
        }
        return $countryCode;
    }

    /**
     * @param $match_staff_info_id
     * @param $staff_info_id
     * @return bool
     */
    public function isValidMatch($match_staff_info_id, $staff_info_id)
    {
        if (empty($match_staff_info_id)) {
            return false;
        }
        $staffInfo = HrStaffInfoModel::findFirst([
            "staff_info_id = :staff_info_id:",
            "bind" => [
                "staff_info_id" => $match_staff_info_id,
            ],
            "columns" => "state,leave_date,is_sub_staff",
        ]);

        if (empty($staffInfo)) {
            $this->logger->write_log(sprintf('isValidMatch staff_info_id:%s not exist', $match_staff_info_id), "info");
            return false;
        }

        if ($staffInfo->state == HrStaffInfoModel::STATE_2) {
            $this->logger->write_log(sprintf('isValidMatch staff_info_id:%s has leave', $match_staff_info_id), "info");
            return false;
        }

        if ($staff_info_id == $match_staff_info_id) {
            $this->logger->write_log(sprintf('isValidMatch staff_info_id:%s is himself', $match_staff_info_id), "info");
            return false;
        }

        //子账号的底片匹配到了主账号
        $supportInfo        = HrStaffApplySupportStoreModel::findFirst("sub_staff_info_id = ". $staff_info_id);
        if (!empty($supportInfo['staff_info_id']) && $supportInfo['staff_info_id'] == $match_staff_info_id) {
            return false;
        }
        return true;
    }

    //口罩识别
    public function maskVerify($staff_info_id, $url)
    {
        $format['bucket'] = $this->config->application->oss_bucket;//固定
        $format['path']   = $url;
        $flag             = 0; //是否拼接内网图片地址
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $url = $this->format_oss($format, $flag);
        $param = sprintf('url=%s&face_attributes=mask&max_face_num=1', $url);

        $maskCheckResult = AiServer::getInstance()->setConfig(enums::IDENTIFY_MASK_CHECK)
            ->sendRequestWithHttpCode($param, $staff_info_id);

        $this->logger->write_log(sprintf('mask_check_ai %s, %s', $param, json_encode($maskCheckResult)), 'info');
        $httpCode = $maskCheckResult['http_code'];
        $res      = $maskCheckResult['response'];

        //如果 没有返回  算未知错误 存-3
        if (empty($res) || $httpCode == 0 || $httpCode >= 500) {
            return -3;
        }

        if (!empty($res['result']) && !empty($res['result']['face_list'][0]['mask']['score'])) {//调用成功 ai 那边说就这个层级 做成服务产品 不能改 封了800层
            return floatval($res['result']['face_list'][0]['mask']['score']);
        }
        if (!empty($res['error'])) {
            return $this->ai_code_format($res['error'], enums::AI_INTERFACE_3);
        }
        return 0;
    }

    /**
     * @param $error_data
     * @param int $type 接口业务类型
     * @return float
     */
    public function ai_code_format($error_data, $type)
    {
        $enum_data = enums::$ai_error_num;
        $code = $error_data['code'];
        $score = -3;
        if (!empty($enum_data[$code])) {
            $score = $enum_data[$code];
        }

        //静默 不是百分制 需要扩大100 所以先缩小100 适应上面的代码
        if ($type == enums::AI_INTERFACE_2) {
            return $score / 100;
        }

        return $score;
    }
    /**
     * 是否是高级主管的虚拟工号
     * @param $staff_info_id
     * @return bool
     */
    public function isSeniorBranchSupervisorVirtualId($staff_info_id): bool
    {
        if (!isCountry('TH')) {
            return false;
        }
        $sql  = 'SELECT * FROM supervisor_store_info WHERE virtual_staff_id = ? and deleted = 0';
        $data = $this->getDI()->get('db_fle')->query($sql, [$staff_info_id])->fetch(\Phalcon\Db::FETCH_ASSOC);
        return !empty($data);
    }

    /**
     * 打卡时，主账户向子账号，或子账号向主账号同步打卡底片
     * @param $params
     * @return void
     */
    public function consumeAttendanceAttachment($params)
    {
        $staffInfoId      = $params['staff_info_id'];
        $attachmentBucket = $params['attachment_bucket'];
        $attachmentPath   = $params['attachment_path'];

        $attachmentInfo = StaffWorkAttendanceAttachmentModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and deleted = 0',
            'bind' => [
                'staff_info_id' => $staffInfoId,
            ],
        ]);
        if (!empty($attachmentInfo)) {
            $this->logger->write_log(sprintf('%d attendance attachment is exists', $staffInfoId), 'info');
            return;
        }
        $model = new StaffWorkAttendanceAttachmentModel();
        $model->staff_info_id = $staffInfoId;
        $model->work_attendance_path = $attachmentPath;
        $model->work_attendance_bucket = $attachmentBucket;
        $model->operate_id = enums::SYSTEM_STAFF_ID;
        $model->operate_name = 'system auto async';
        $model->save();
    }

    //泰国 个人代理 通知 fbi
    public function sendIcOffCard($staffId, $attendanceDate, $time)
    {
        $data = [
            'staff_info_id'   => $staffId,
            'attendance_date' => $attendanceDate,
            'off_work_time'   => $time,
        ];
        $this->logger->write_log(sprintf("sendIcOffCard_{$staffId} " . json_encode($data)), 'info');
        $sendData['jsonCondition']    = json_encode($data);
        $sendData['handleType']       = RocketMQ::TAG_INCENTIVE_COURIER_PUNCH_OUT;
        $sendData['shardingOrderKey'] = $staffId;
        $rmq                          = new RocketMQ('incentive_courier_punch_out');
        return $rmq->sendOrderlyMsg($sendData);//有序
    }


    /**
     * 作弊验证 取配置 提示再次尝试刷脸
     * @param $staffId
     * @param $attendance_date
     * @return bool
     * @throws ValidationException
     */
    public function checkFailTimes($staffId, $attendance_date)
    {
        //数据查询记录轮数
        $count = StaffWorkDetectFaceRecordModel::count([
            'conditions' => 'staff_info_id = :staff_id: AND attendance_date = :date_at:',
            'bind'       => ['staff_id' => $staffId, 'date_at' => $attendance_date],
        ]);
        $count++;
        $key        = "{$staffId}_{$attendance_date}_{$count}";
        $redis      = $this->getDI()->get('redisLib');
        $cacheCount = $redis->get($key);
        //本轮第一次 需要提示
        if (empty($cacheCount)) {
            $redis->set($key, 1, 10 * 3600);
            $cacheCount = 0;
        }
        //查询配置次数
        $setting_model = new BySettingRepository($this->lang);
        $setting       = $setting_model->get_setting('check_face_recognition_frequency');
        $setting       = intval($setting);
        $this->logger->write_log("checkFailTimes_{$staffId} setting {$setting} cache {$cacheCount}", 'info');
        if (empty($setting)) {
            return true;
        }
        $cacheCount++;
        $redis->set($key, $cacheCount, 10 * 3600);
        //没到最大次数 继续提示
        if ($cacheCount < $setting) {
            //需要再次尝试
            http_response_code(ErrCode::IMAGE_VERIFY_TRY_AGAIN_HTTP);
            throw new ValidationException($this->getTranslation()->_('face_detect_fail'), ErrCode::IMAGE_VERIFY_TRY_AGAIN);
        }
        return true;
    }

    /**
     * 验证人脸黑名单
     * @param $staff_info_id
     * @param $source_url
     * @return true
     * @throws ValidationException
     */
    public function dealFaceBlackListLogic($staff_info_id,$source_url,$is_set_422 = true)
    {
        if ($master_staff_id = StaffRepository::getMasterStaffIdBySubStaff($staff_info_id)) {
            $this->logger->write_log([
                'dealFaceBlackListLogic_sub_staff_change' => [
                    'staff_info_id' => $staff_info_id,
                    'master_staff_id' => $master_staff_id,
                ],
            ], 'info');
            $staff_info_id = $master_staff_id;
        }

        $format['bucket'] = $this->config->application->oss_bucket;
        $format['path']   = $source_url;
        if (RUNTIME == 'pro') {
            $flag = 1;
        }
        $_source_url = $this->format_oss($format, $flag ?? 0);
        //调用ai接口 返回工号和证件号
        $faceBlacklist                     = $this->checkInFaceBlacklist($staff_info_id, $_source_url);
        $params['staff_info_id']           = $staff_info_id;
        $params['face_blacklist_identity'] = $faceBlacklist ? $faceBlacklist->identity : '';
        $params['match_face_blacklist_id'] = $faceBlacklist ? $faceBlacklist->id : 0;
        $params['match_staff_id']          = $faceBlacklist ? $faceBlacklist->staff_info_id : 0;
        $params['action_time']             = date('Y-m-d H:i:s');
        $params['business_type']           = 'deal_face_blacklist';
        $params['staff_lang']              = $this->lang;
        $params['face_img_url']            = $source_url;
        $mq                                = new RocketMQ('staff-face-blacklist');
        $mq->sendOrderlyMsg($params);

        if(!empty($faceBlacklist)){
            //保存底片
            $insert['staff_info_id']          = $staff_info_id;
            $insert['work_attendance_path']   = $source_url;
            $insert['work_attendance_bucket'] = $this->config->application->oss_bucket;
            $this->save_face_img($insert);
            $is_set_422 && http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('20804_account_error'),
                ErrCode::AI_IMAGE_VERIFY_ERROR);
        }
        return true;
    }

    /**
     * @param $staff_info_id
     * @param $source_url
     * @throws ValidationException
     */
    public function checkInFaceBlacklist($staff_info_id,$source_url)
    {

        $countryCode    = strtolower(env('country_code', 'Th'));    

        //生成唯一ID
        $nodeProvider = new RandomNodeProvider();
        $uuid         = Uuid::uuid1($nodeProvider->getNode(), mt_rand(1, 16000))->toString();
        $params       = [
            "url"           => $source_url,
            "request_id"    => $uuid,
            "country"       => $countryCode . '-blacklist',
            "group_id_list" => ['1'],
        ];
        $result       = AiServer::getInstance()->setConfig(enums::IDENTIFY_FACE_BLACKLIST)->sendEx(json_encode($params),
            $staff_info_id);
        if (empty($result) || !empty($result['error'])) {
            http_response_code(422);
            throw new ValidationException($this->getTranslation()->_('4008'),
                ErrCode::AI_IMAGE_VERIFY_MISS_ARGS);
        }
        //搜索到人脸时
        //score字段大于等于93分，flash_hr_exist = true
        if (isset($result['result']['flash_hr_exist']) && $result['result']['flash_hr_exist']) { //发现比对的问题
            //获取匹配到的员工底片
            $staffInfoId = $result['result']['person_id'];
            //看是否已经被移除了
            $staffFaceBlacklist = StaffFaceBlacklistModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and is_deleted = 0',
                'bind'       => ['staff_info_id' => $staffInfoId],
                'order'      => 'id desc',
            ]);
            if (empty($staffFaceBlacklist)) {
                return false;
            }
            //泰国匹配存在ic跳过的场景
            $isSkip = (new StaffFaceBlacklistServer())->isSkip($staff_info_id, $staffFaceBlacklist);
            if ($isSkip) {
                return false;
            }
            
            return $staffFaceBlacklist;
        }

        return false;
    }



    //当前登陆账号 是否在支援状态 包括主账号
    public function checkSupportFlag($staff_id, $attendance_date){
        //是否支援 和 主账号是否休息
        $att_re = new AttendanceRepository($this->lang, $this->timezone);
        if($this->isSupport == false){
            //当前登陆的是主账号 要去查询子账号是否支援
            $this->supportInfo = $att_re->getSupportOsStaffInfo($staff_id, $attendance_date);
            if(!empty($this->supportInfo)){
                $this->isMasterSupport = true;
            }
            return ;
        }
        //子账号登陆 并且支援状态 肯定有支援信息
        if(empty($this->supportInfo)){
            return ;
        }
        $staff_re = new StaffRepository($this->lang);
        //当前登陆的是子账号 是支援状态 要查询主账号的 只有马来和泰国 菲律宾没有子账号
        if(isCountry('TH') || isCountry('MY')){
            $this->masterStaffInfo = $staff_re->getStaffpositionV2($this->supportInfo['staff_info_id']);
        }
        return ;
    }

    //获取居家办公日期
    public function getWorkHomeDates($dateList, $staffInfo)
    {
        if (empty($dateList)) {
            return [];
        }

        //只有总部员工有配置
        if ($staffInfo['sys_store_id'] != enums::HEAD_OFFICE_ID) {
            return [];
        }

        //工作所在州 必填
        $staffProvinceCodeList = HrStaffItemsModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and item = :item:',
            'bind'       => ['staff_info_id' => $staffInfo['staff_info_id'], 'item' => 'STAFF_PROVINCE_CODE'],
        ]);
        $provinceCode = 'MY06';
        if(!empty($staffProvinceCodeList)){
            $provinceCode = $staffProvinceCodeList->value;
        }

        //整理 成星期对应日期
        $weekDateList = formatDateWeek($dateList);
        $allWeeks = array_keys($weekDateList);
        //先获取员工的 居家办公日期
        $staffWH = WorkHomeStaffSettingModel::find([
            'columns' => 'staff_info_id, date_at, weekday',
            'conditions' => 'staff_info_id = :staff_id: and (date_at in ({dates:array}) or weekday in ({weeks:array})) and is_delete = 0',
            'bind' => ['staff_id' => $staffInfo['staff_info_id'], 'dates' => $dateList, 'weeks' => $allWeeks],
        ])->toArray();

        $staffWHData = [];
        foreach ($staffWH as $item){
            if(!empty($item['weekday'])){
                $staffDates = $weekDateList[$item['weekday']];
                $staffWHData = array_merge($staffWHData, $staffDates);
            }else{
                $staffWHData[] = $item['date_at'];
            }
        }
        $staffWH = null;

        //居家办公日期
        $homeData = WorkHomeSettingModel::find([
            'conditions' => 'node_department_id = :node_department_id: and (date_at in ({dates:array}) or weekday in ({weeks:array}))
                and hire_type = :hire_type: and province_code = :province_code: and  is_delete = 0
                and (job_title is null or job_title = :job_title:)',
            'bind'       => [
                'node_department_id' => $staffInfo['node_department_id'],
                'hire_type'          => $staffInfo['hire_type'],
                'province_code'      => $provinceCode,
                'dates'              => $dateList,
                'weeks'              => $allWeeks,
                'job_title'          => $staffInfo['job_title'],
            ],
        ])->toArray();
        if (empty($homeData)) {
            return array_values(array_unique($staffWHData));
        }

        $dates = empty($homeData) ? [] : array_column($homeData, 'date_at');
        $dates = array_diff($dates, ['0000-00-00']);
        $dates = array_merge($staffWHData, $dates);//合并

        $weeks = empty($homeData) ? [] : array_column($homeData, 'weekday');
        $weeks = array_diff($weeks,[0]);
        if(!empty($weeks)){//按星期配置
            foreach ($weeks as $w){
                $dates = array_merge($dates, $weekDateList[$w] ?? []);
            }
        }
        $dates = array_values(array_unique($dates));
        return $dates;
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同 - 发送协议消息
     * @param array $params 参数组
     * @return bool
     */
    public function sendOsStaffContractMessage($params)
    {
        $db = $this->getDI()->get('db');
        $db->begin();
        $osStaffContractSignInData = [];
        try {
            $staffInfoId = $params['staff_info_id'];
            if (empty($staffInfoId)) {
                throw new ValidationException('员工ID为空，不处理');
            }

            $signTemplateUrl = (new SettingEnvServer())->getSetVal('os_staff_contract_sign_template_url');
            if (empty($signTemplateUrl)) {
                throw new Exception('外协外协仓管自动发送合同协议模版链接,未配置，请联系产品处理');
            }

            $staffInfo = (new StaffRepository())->getStaffInfoOne($staffInfoId);
            if (empty($staffInfo) || $staffInfo['formal'] != HrStaffInfoModel::FORMAL_0) {
                throw new ValidationException('根据工号未查询到信息或不是非编制（外协），不处理');
            }

            if (empty($staffInfo['identity'])) {
                throw new ValidationException('员工的身份证号为空（不用区分是身份证还是护照），不处理');
            }

            //外协员工的公司需要是Premier，查不到或非此公司，不处理
            $osStaffExtendInfo = OsStaffInfoExtendModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and company_item_id = :company_item_id:',
                'bind'       => [
                    'staff_info_id'   => $staffInfoId,
                    'company_item_id' => OsStaffInfoExtendModel::COMPANY_PREMIER_ID,
                ],
            ]);
            if (empty($osStaffExtendInfo)) {
                throw new ValidationException('外协员工的公司查不到或非Premier公司，不处理');
            }

            //获取外协仓管自动发送合同签署消息关联信息
            $signRecord = OsStaffContractSignRecordModel::find([
                'conditions' => 'identity = :identity:',
                'bind'       => ['identity' => $staffInfo['identity']],
            ])->toArray();

            //获取签署日期
            $signedAt = $signRecord ? array_unique(array_column($signRecord, 'signed_at')) : [];
            //没有记录或记录的签署日期均为空-未签署
            $state = OsStaffContractSignRecordModel::STATE_UNSIGNED;
            if (empty($signedAt) || (count($signedAt) == 1 && empty($signedAt[0]))) {
                $state = OsStaffContractSignRecordModel::STATE_UNSIGNED;
            } else {
                //有签署日期但超过一年：今天-上次签署日期<=365天视为一年内，无一年内为未签署
                $currentTimestamp = strtotime(date('Y-m-d'));
                //计算天数差（向下取整确保整天数）
                foreach ($signedAt as $signed_at) {
                    $daysDifference = floor(($currentTimestamp - strtotime($signed_at)) / 86400);
                    $this->logger->write_log('签署日期：' . $signed_at . '，距离当前日期天数差为：' . $daysDifference, 'info');
                    if ($daysDifference <= 365) {
                        //有一年内的视为有签署
                        $state = OsStaffContractSignRecordModel::STATE_SIGNED;
                        break;
                    }
                }
            }

            //已签署，不处理
            if ($state == OsStaffContractSignRecordModel::STATE_SIGNED) {
                throw new ValidationException('已签署，不处理');
            }

            //生产pdf，注入表
            $agreementInData = ['name' => '', 'sign_url' => ''];
            $singPdfSetting  = [
                'displayHeaderFooter' => true,
                'headerTemplate'      => '',
                'footerTemplate'      => '',
            ];
            $pdfRes          = (new formPdfServer())->getInstance()->generatePdf($signTemplateUrl, $agreementInData, [],
                '', $singPdfSetting);

            if (!isset($pdfRes['object_url'])) {
                throw new Exception('generatePdf object_url Error!');
            }

            //未签署,记录外协仓管自动发送合同签署信息
            $osStaffContractSignInData = [
                'staff_info_id' => $staffInfoId,
                'message_id'    => '',
                'name'          => $staffInfo['name'],
                'identity'      => $staffInfo['identity'],
                'pdf_url'       => $pdfRes['object_url'],
                'sign_pdf_url'  => '',
                'sign_url'      => '',
                'state'         => $state,
                'signed_at'     => null,
                'operator_id'   => 0,
            ];

            $model                  = new OsStaffContractSignRecordModel();
            $osStaffContractSignRes = $model->save($osStaffContractSignInData);
            if (!$osStaffContractSignRes) {
                throw new Exception('OsStaffContractSignRecord Save Error!');
            }

            $businessId = $model->id;

            //发送消息
            $messageData = [
                'staff_info_ids_str' => $staffInfoId,
                'push_state'         => 1,
                'audit_status'       => 2,//已通过
                'staff_users'        => [$staffInfoId],
                'message_title'      => 'กรุณาลงชื่อในสัญญารับจ้างจัดการพัสดุ',
                'message_content'    => '',
                'category'           => MessageEnums::CATEGORY_SIGN,
                'category_code'      => EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT'),
                'id'                 => time() . $staffInfoId . rand(1000000, 9999999),
            ];
            $hcmRpc      = (new ApiClient('hcm_rpc', '', 'add_kit_message', $params['lang']));
            $hcmRpc->setParams($messageData);
            $messageRet = $hcmRpc->execute();
            $messageId  = '';
            if (isset($messageRet['result']['code']) && $messageRet['result']['code'] == ErrCode::SUCCESS) {
                $messageId = $messageRet['result']['data'][0];
            }
            if (!$messageId) {
                throw new Exception('add_kit_message send Error!');
            }

            //更新数据表
            $res = $db->updateAsDict(
                $model->getSource(),
                ['message_id' => $messageId],
                [
                    'conditions' => 'id = ?',
                    'bind'       => [
                        $businessId,
                    ],
                    'bindTypes'  => [\PDO::PARAM_STR],
                ]
            );

            if (!$res) {
                throw new Exception('OsStaffContractSignRecord MessageId Update Error!');
            }

            return $db->commit();
        } catch (ValidationException $e) {
            $db->rollBack();
            //记日志
            $this->logger->write_log(['sendOsStaffContractMessage-ValidationException ' => $params,  'message' => $e->getMessage()],
                'info');
            return true;
        } catch (Exception $e) {
            $db->rollBack();
            $this->logger->write_log(['sendOsStaffContractMessage-Exception ' => $osStaffContractSignInData, 'message' => $e->getMessage() . ', 行号：' . $e->getLine()]);
            return false;
        }
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同 - 获取消息内容
     * @param array $params 参数组
     * @return array
     */
    public function getOsStaffContractMsg($params)
    {
        $sign = OsStaffContractSignRecordModel::findFirst([
            'conditions' => 'message_id = :message_id: and staff_info_id = :staff_info_id:',
            'bind'       => ['message_id' => $params['msg_id'], 'staff_info_id' => $params['staff_info_id']],
        ]);

        $pdf_url = '';
        $state   = OsStaffContractSignRecordModel::STATE_UNSIGNED;
        if ($sign) {
            if ($sign->state == OsStaffContractSignRecordModel::STATE_UNSIGNED) {
                $pdf_url = $sign->pdf_url;
                $state   = $sign->state;
            } else {
                $pdf_url = $sign->sign_pdf_url;
                $state   = $sign->state;
            }
        }

        return [
            'pdf_url' => $pdf_url,
            'state'   => $state,
        ];
    }

    /**
     * 21322【TH|BY|消息】 外协仓管自动发送合同 - 签署协议
     * @param array $params 参数组
     * @return bool
     * @throws ValidationException
     */
    public function signOsStaffContractSubmit($params)
    {
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //先获取外协仓管自动发送合同未签署信息
            $model = OsStaffContractSignRecordModel::findFirst([
                'conditions' => 'message_id = :message_id: and staff_info_id = :staff_info_id: and state = :state:',
                'bind'       => [
                    'message_id'    => $params['msg_id'],
                    'staff_info_id' => $params['staff_info_id'],
                    'state'         => OsStaffContractSignRecordModel::STATE_UNSIGNED,
                ],
            ]);
            if (empty($model)) {
                throw new ValidationException($this->getTranslation()->_('21322_by_error_message_001'),
                    ErrCode::VALIDATE_ERROR);
            }

            //获取未读消息
            $category_code  = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_SEND_OS_STAFF_CONTRACT');
            $messageCourier = MessageCourierModel::findFirst([
                'conditions' => 'id = :id: and category = :category: and category_code = :category_code: and read_state = :read_state:',
                'bind' => [
                    'id'            => $params['msg_id'],
                    'category'      => MessageEnums::CATEGORY_SIGN,
                    'category_code' => $category_code,
                    'read_state'    => MessageCourierModel::READ_STATE_UNREAD,
                ],
            ]);
            if (empty($messageCourier)) {
                throw new ValidationException($this->getTranslation()->_('21322_by_error_message_003'),
                    ErrCode::VALIDATE_ERROR);
            }

            //生产pdf，注入表
            $signTemplateUrl = (new SettingEnvServer())->getSetVal('os_staff_contract_sign_template_url');
            $agreementInData = ['name' => $model->name];
            $img_data        = [['name' => 'sign_url', 'url' => $params['sign_url']]];
            $singPdfSetting  = [
                'displayHeaderFooter' => true,
                'headerTemplate'      => '',
                'footerTemplate'      => '',
            ];
            $pdfRes          = (new formPdfServer())->getInstance()->generatePdf($signTemplateUrl, $agreementInData,
                $img_data, '', $singPdfSetting);

            if (!isset($pdfRes['object_url'])) {
                throw new ValidationException($this->getTranslation()->_('21322_by_error_message_002'),
                    ErrCode::VALIDATE_ERROR);
            }

            //签署
            $model->sign_pdf_url    = $pdfRes['object_url'];
            $model->sign_url        = $params['sign_url'];
            $model->state           = OsStaffContractSignRecordModel::STATE_SIGNED;
            $model->signed_at       = date('Y-m-d');
            $model->operator_id     = $params['staff_info_id'];
            $osStaffContractSignRes = $model->save();
            if (!$osStaffContractSignRes) {
                throw new Exception($this->getTranslation()->_('21322_by_error_message_007'));
            }

            //改为已读
            $messageCourier->read_state = MessageCourierModel::READ_STATE_HAS_READ;
            $messageCourierRet          = $messageCourier->save();
            if (!$messageCourierRet) {
                throw new Exception($this->getTranslation()->_('21322_by_error_message_004'));
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollBack();
            throw $e;
        } catch (Exception $e) {
            $db->rollBack();
            $this->logger->write_log(['signOsStaffContractSubmit-Exception' => $params, 'message' => $e->getMessage() . ', 行号：' . $e->getLine()]);
            throw $e;
        }
        return true;
    }

    /**
     * @param $staff_info_id
     * @param $attendance_date
     * @return bool
     */

    public function sendOcwAgreementMessage($params): bool
    {
        if (empty($params['staff_info_id']) || empty($params['attendance_date'])) {
            return false;
        }
        $db                           = $this->getDI()->get("db");
        $db->begin();
        try {
            $staffInfo            = (new HrStaffInfoModel())->getOneByStaffId($params['staff_info_id']);
            if ($staffInfo['sys_store_id'] != "-1") {
                $storeObj = SysStoreModel::findFirst([
                    'conditions' => "id = :store_id:",
                    'bind'       => [
                        'store_id' => $staffInfo['sys_store_id'],
                    ],
                    "columns"    => 'id,name',
                ]);
                $staffInfo['store_name'] = (is_object($storeObj) && !empty($storeObj->toArray())) ? $storeObj->name : "";
            }
            if ($staffInfo['sys_store_id'] == "-1") { // 总部
                $staffInfo['store_name'] = enums::HEAD_OFFICE;
            }
            $staff_name = $staffInfo['name'] ?? '';
            $msgId                                 = time() . $params['staff_info_id'] . rand(1000000, 9999999);
            $lang                                  = (new StaffServer())->getLanguage($params['staff_info_id']);
            $messageSendData['staff_users']        = [$params['staff_info_id']];
            $messageSendData['message_title']      = $this->getTranslationByLang($lang)->_('ocw_agreement_title',['attendance_date'=>$params['attendance_date'],'staff_name'=>$staff_name]);
            $messageSendData['message_content']    = '';
            $messageSendData['staff_info_ids_str'] = $params['staff_info_id'];
            $messageSendData['id']                 = $msgId;
            $messageSendData['category']           = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_OCW_AGREEMENT');
            $res                                   = (new MessageCourierServer())->add_kit_message($messageSendData);
            if (!isset($res[1]) || $res[1] != 1) {
                throw new Exception('发送消息失败');
            }
            $pdf_data = $this->generatePdfOcwAgreement($staffInfo, $params);
            if (empty($pdf_data[0])){
                throw new Exception('生成pdf失败');
            }
            $messagePdf                        = new MessagePdfModel();
            $messagePdfData['staff_info_id']   = $params['staff_info_id'];
            $messagePdfData['module_category'] = MessagePdfModel::MODULE_CATEGORY_OCW_AGREEMENT_MESSAGE;
            $messagePdfData['pdf_url']         = $pdf_data[0];
            $messagePdfData['msg_id']          = $msgId;
            $messagePdfData['form_data_json']  = !empty($pdf_data[1]) ? json_encode($pdf_data[1], JSON_UNESCAPED_UNICODE) : '';
            $res = $messagePdf->save($messagePdfData);
            if (!$res) {
                throw new Exception('MessagePdf Save Error!');
            }
            $db->commit();
        } catch (Exception|BusinessException|ValidationException $e) {
            $db->rollback();
            $this->getDI()->get("logger")->write_log([
                'function' => __FUNCTION__,
                'error'    => $e->getMessage(),
                'params'   => $params,
                'trace'    => $e->getTraceAsString(),
            ]);
            return false;
        }
        return true;
    }
    /**
     * 判断是否为半天班次
     * @param $shiftStartTime
     * @param $shiftEndTime
     * @return bool
     */
    private function isHalfDayShift($shiftStartTime,$shiftEndTime): bool
    {
        if (empty($shiftStartTime) || empty($shiftEndTime)) {
            return false;
        }
        $shiftDuration = ($shiftEndTime - $shiftStartTime) / 3600;
        // 班次时长小于等于4小时判定为半天班次
        return $shiftDuration <= 4;
    }

    private function formatDate($date, $shift_time)
    {
        if (empty($date)) {
            return 0;
        }
        return strtotime(sprintf('%s %s', $date, $shift_time));
    }

    /**
     * @param $staffInfo
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function generatePdfOcwAgreement($staffInfo, $params): array
    {
        if (empty($staffInfo) || empty($params['attendance_date'])) {
            return [];
        }
        //考虑夸天班次的问题
        $shiftStartTime = $params['shift_start'] ? $this->formatDate($params['attendance_date'], $params['shift_start']) : '';
        if ($params['shift_start'] > $params['shift_end']) {
            $tomorrowDate = date('Y-m-d', strtotime($params['attendance_date'] . '+1 day'));
            $shiftEndTime = $params['shift_end'] ? $this->formatDate($tomorrowDate, $params['shift_end']) : '';
        } else {
            $shiftEndTime = $params['shift_end'] ? $this->formatDate($params['attendance_date'], $params['shift_end']) : '';
        }
        $duration_program = '';
        if (!empty($params['shift_id'])){
            $duration_program = '8 hours, exclusive of one (1) hour break';
            if ($this->isHalfDayShift($shiftStartTime,$shiftEndTime)){
                $duration_program = '4 hours';
            }
        }
        $queryParams = [
            'staff_info_id' => $staffInfo['staff_info_id'],
        ];
        if (isCountry('PH')){
            $queryParams['nationality'] = 4;
        }
        $pdfHelperServer = new PdfHelperServer();
        $rpcSetting = $pdfHelperServer->getHeaderAndFooter($queryParams);
        $html_oss_url = BASE_PATH.'/public/pdf_template/os_owc_agreement.ftl';
        $html_oss_url = (new ToolServer($this->lang, $this->timezone))->getPdfTemp($html_oss_url);
        
        $form_data = [
            'attendance_date'   => $params['attendance_date'],
            'staff_info_id'     => $staffInfo['staff_info_id'] ?? '',
            'store_name'        => $staffInfo['store_name'] ?? '',
            'duration_program'  => $duration_program,
            'company_full_name' => $rpcSetting['company_full_name'] ?? '',
        ];
        // 产品说不要页眉页脚
        $pdf_header_footer_setting = [
            'displayHeaderFooter' => false,
            'headerTemplate' => [],
            'footerTemplate' => []
        ];
        $form_data['pdf_options'] = $pdf_header_footer_setting;
        $form_data['pdf_temp_url'] = $html_oss_url;
        $file_name                 = 'ocw_agreement' . $staffInfo['staff_info_id'] . time(). '.pdf';
        $pdf_file_data = (new formPdfServer())->generatePdf($html_oss_url, $form_data, [], $file_name, $pdf_header_footer_setting, 'attchment');
        if (empty($pdf_file_data['object_url'])){
            $this->getDI()->get("logger")->write_log([
                'function'  => __FUNCTION__,
                'error'     => '生成pdf失败',
                'form_data' => $form_data,
            ]);
            return [];
        }
        return [$pdf_file_data['object_url'],$form_data];
    }

    /**
     * @param $param
     * @return bool
     * @throws ValidationException
     */
    public function submitMsgSignImg($param): bool
    {
        $userId     = $param['user_id'] ?? 0;
        $msgId      = $param['msg_id'] ?? '';
        $signImgUrl = $param['sign_img_url'] ?? '';
        if (empty($msgId)) {
            throw new ValidationException($this->getTranslation()->_('miss_args'));
        }
        $server = new BackyardServer($this->lang, $this->timezone);
        // 验证该员工是否收到签字消息
        $sign_msg = $server->get_staff_sign_msg([
            'msg_id'        => $msgId,
            'staff_info_id' => $userId,
        ]);
        if (empty($sign_msg) || $sign_msg['staff_info_id'] != $userId) {
            throw new ValidationException('员工没有收到该签字消息');
        }
        $messagePdfObj = MessagePdfModel::findFirst([
            'conditions' => "msg_id = :msg_id:",
            'bind'       => ['msg_id' => $msgId],
        ]);
        if (empty($messagePdfObj) || empty($messagePdfObj->form_data_json)) {
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
        if (!empty($messagePdfObj->sign_img_url)) {
            throw new ValidationException($this->getTranslation()->_('message_sign_err'));
        }
        $messagePdf = $messagePdfObj->toArray();
        $form_data  = json_decode($messagePdf['form_data_json'], true);
        $form_data['staff_sign_url'] = $signImgUrl;
        $_pdfImgData                  = [
            ['name' => 'staff_sign_img', 'url' => $form_data['staff_sign_url']],
        ];
        $new_time                     = time();
        $this->add_hour               = $this->config->application->add_hour;
        $_sign_time                   = gmdate('Y-m-d H:i:s', $new_time + ($this->add_hour) * 3600);
        $form_data['staff_sign_time'] = $_sign_time;
        $db                           = $this->getDI()->get("db");
        $db->begin();
        try {
            $pdf_header_footer_setting = $form_data['pdf_options'];
            $pdf_template_url          = $form_data['pdf_temp_url'];
            $file_name                 = 'ocw_agreement' . $userId . time(). '.pdf';
            $pdf_file_data             = (new formPdfServer())->generatePdf($pdf_template_url, $form_data, $_pdfImgData,
                $file_name, $pdf_header_footer_setting, 'attchment');
            if (empty($pdf_file_data['object_url'])) {
                throw new \Exception('pdf error ');
            }
            $messagePdfObj->sign_url     = $pdf_file_data['object_url'];
            $messagePdfObj->sign_time    = gmdate('Y-m-d H:i:s', $new_time);
            $messagePdfObj->sign_img_url = $signImgUrl;
            $messagePdfObj->state        = MessagePdfModel::STATE_SUCCESS;
            $messagePdfObj->save();
            // 将消息 已读
            $backyardServer = new BackyardServer($this->lang, $this->timeZone);
            $backyardServer->has_read_operation($msgId, true);
            $backyardServer->addHaveReadMsgToMns($msgId);
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDI()->get("logger")->write_log([
                'function' => __FUNCTION__,
                'error'    => $e->getMessage(),
            ]);
            throw new ValidationException($this->getTranslation()->_('data_error'));
        }
    }
}