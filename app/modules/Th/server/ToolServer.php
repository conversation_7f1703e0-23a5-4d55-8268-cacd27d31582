<?php

namespace FlashExpress\bi\App\Modules\Th\Server;

use FlashExpress\bi\App\Enums\MenuEnums;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Server\ToolServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\VehicleInspectionServer;

class ToolServer extends GlobalBaseServer
{
    /**
     * 获取个人信息菜单列表
     * @param $params
     * @return array
     */
    public function getPersonalInformationMenuList($params): array
    {
        $platform = $params['platform'] ?? enums::FB_BACKYARD;
         $show  = $this->staffInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_UN_PAID;

        //姓名工号
        $menu_list[] = $this->getMenuStaffName($params);
        //基本信息
        $menu_list[] = $this->getMenuBaseInformation($params);
        //电子合同
        $menu_list[] = $this->getMenuElectronicContract($params);

        //保险受益人
        $menu_list[] = $this->getMenuInsuranceBeneficiary($params);

        if ($show) {
            //工资
            $menu_list[] = $this->getMenuSalary($params);
        }
        if ($this->incentiveShow) {
            //提成
            $menu_list[] = $this->getMenuCommission($params);
        }
        if ($show) {
            //犯罪记录
            $menu_list[] = $this->getMenuCriminalRecord($params);
        }

        // 车辆信息(v21270 增加BY入口)
        $menu_list[] = $this->getMenuVehicleInfo($params);

        //新增 车厢信息 24年12月7号之后入职的才有
        $menu_list[] = $this->getVanContainer($params);

        //个人代理 展示 付款凭证 代扣税凭证
        if(!$show) {
            $menu_list[] = $this->getPaymentVoucherInfo($params);//付款凭证
            $menu_list[] = $this->getTaxWithholdingVoucherInfo($params);//代扣税凭证
        }
        //车辆稽查登记
        if ($vehicleInspectionMenu = $this->vehicleInspectionMenu()) {
            $menu_list[] = $vehicleInspectionMenu;
        }

        return array_values(array_filter($menu_list));
    }

    /**
     * 车辆稽查登记
     * @return array|false
     */
    protected function vehicleInspectionMenu()
    {
        $server = new VehicleInspectionServer($this->lang, $this->timezone);
        if (!$server->enableMobileVersion()) {
            return false;
        }
        $is_show_vehicle_inspection = $server->isShowVehicleInspection($this->staffInfo['id']);

        if (!$is_show_vehicle_inspection) {
            return false;
        }

        $right_state_text = $this->t->_('need_vehicle_inspection_tips');//请提交车辆稽查登记
        $right_state_text_background_color = 'FFEA33';

        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_vehicle_inspection',
            'title' => $this->t->_('personal_information_menu_vehicle_inspection'), //菜单标题
            'type'  => MenuEnums::MENU_DST_TYPE_NATIVE_PAGE, //菜单类型
            'dst'   => 'flashbackyard://fe/page?path=carInspect', //跳转链接
            'right_state_text' => $right_state_text, //右侧状态文本
            'right_state_text_background_color' => $right_state_text_background_color, //状态文字背景色
        ]);
    }


    public function getPaymentVoucherInfo($params)
    {
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_payment_voucher',
            'title' => $this->t->_('personal_information_menu_payment_voucher'),
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD,
            'dst'   => env('h5_endpoint') . 'payment-voucher', //跳转链接
        ]);
    }

    public function getTaxWithholdingVoucherInfo($params)
    {
        return $this->combinationMenuParams([
            'id'    => 'personal_information_menu_tax_withholding_voucher',
            'title' => $this->t->_('personal_information_menu_tax_withholding_voucher'),
            'type'  => MenuEnums::MENU_DST_TYPE_H5_PAGE_PASSWORD,
            'dst'   => env('h5_endpoint') . 'tax-withholding-voucher', //跳转链接
        ]);
    }

    /**
     * 保险受益人
     * @param $params
     * @return array
     */
    public function getMenuInsuranceBeneficiary($params): array
    {

        $staffInfoId = $this->staffInfo['id'] ?? 0;

        $server = new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone);
        if (!$server->verifyStaffInsuranceBeneficiaryCollectMenu($staffInfoId)) {
            return [];
        }

        $rightStateText      = '';
        $rightStateTextColor = '';

        //检查用户是否提交
        if (!$server->checkStaffSubmit($staffInfoId)) {
            $rightStateText      = $this->t->_('insurance_beneficiary_submit_state_0');
            $rightStateTextColor = 'FFEA33';
        }

        //返回菜单结构体
        return $this->combinationMenuParams([
            'id'                                => 'personal_information_menu_insurance_beneficiary',
            'title'                             => $this->t->_('personal_information_menu_insurance_beneficiary'),
            //菜单标题
            'type'                              => MenuEnums::MENU_DST_TYPE_H5_PAGE,
            //菜单类型
            'dst'                               => env('h5_endpoint') . 'insurance-beneficiary',
            //跳转链接
            'right_state_text'                  => $rightStateText,
            //右侧状态文本
            'right_state_text_background_color' => $rightStateTextColor,
            //状态文字背景色
        ]);
    }

    /**
     * 个人信息菜单 - 基本信息 默认显示 h5链接 有红点 无右侧文字 无右侧状态文本
     * @param $params
     * @return array
     */
    public function getMenuBaseInformation($params): array
    {
        $staff_info_id = $this->staffInfo['id'];
        $read_count    = $this->getMenuBaseInformationRedCount($staff_info_id);

        return $this->combinationMenuParams([
            'id'         => 'personal_information_menu_base_information',
            'title'      => $this->t->_('personal_information_menu_base_information'), //菜单标题
            'type'       => MenuEnums::MENU_DST_TYPE_H5_PAGE,                          //跳转类型
            'dst'        => env('sign_url') . '/#/BasicInfo',                   //跳转链接
            'read_count' => !empty($read_count) ? (string)$read_count : '',            //红点数
        ]);
    }

    /**
     * 基本信息红点菜单
     * @param $staff_info_id
     * @return int
     */
    public function getMenuBaseInformationRedCount($staff_info_id): int
    {
        $read_count = 0;

        $annexRet  = HrStaffAnnexInfoModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staff_info_id],
        ])->toArray();
        $annexList = array_column($annexRet, null, 'type');


        $hr_staff_info = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
            'columns'    => 'bank_no',
        ]);

        // 身份证
        if (empty($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]) || !in_array($annexList[HrStaffAnnexInfoModel::TYPE_ID_CARD]['audit_state'],[HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED,HrStaffAnnexInfoModel::AUDIT_STATE_PASSED])) {
            $read_count++;
        }

        //银行卡号为空 或者 审核被拒绝 都需要有数字1的提示
        if ((empty($hr_staff_info->bank_no)) || empty($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['annex_path_front'])  ||  (isset($annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]) && $annexList[HrStaffAnnexInfoModel::TYPE_BANK_CARD]['audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
            $read_count++;
        }

        // 户口簿 有一张没上传 则视为 待上传。
        if (empty($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]) || is_null($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state']) || !in_array($annexList[HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD]['audit_state'],[HrStaffAnnexInfoModel::AUDIT_STATE_NOT_REVIEWED,HrStaffAnnexInfoModel::AUDIT_STATE_PASSED])) {
            $read_count++;
        }

        return $read_count;
    }

    /**
     * 获取个人信息红点总数
     * @param $staff_info_id
     * @return int
     */
    public function getPersonalInformationRedCount($staff_info_id): int
    {
        $read_count = $this->getMenuBaseInformationRedCount($staff_info_id);

        //保险受益人
        $insuranceBeneficiaryServer = new StaffInsuranceBeneficiaryServer($this->lang, $this->timezone);

        //检查是否有权限
        if ($insuranceBeneficiaryServer->verifyStaffInsuranceBeneficiaryCollectMenu($staff_info_id)) {
            //检查是否提交
            if (!$insuranceBeneficiaryServer->checkStaffSubmit($staff_info_id)) {
                $read_count++;
            }
        }

        return $read_count;
    }
}