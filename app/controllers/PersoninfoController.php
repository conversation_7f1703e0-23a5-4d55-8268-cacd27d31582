<?php
namespace FlashExpress\bi\App\Controllers;

use App\Country\Tools;
use Exception;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Enums\CeoMailEnums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\ErrCode;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\SalaryGongZiModel;
use FlashExpress\bi\App\Server\CompanyMobileServer;
use FlashExpress\bi\App\Server\LoginServer;
use FlashExpress\bi\App\Server\PasswordServer;
use FlashExpress\bi\App\Server\PersoninfoServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\library\PasswordHash;
use FlashExpress\bi\App\Server\AttendanceServer;
use FlashExpress\bi\App\Server\SysServer;

class PersoninfoController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();
        $method = $this->request->getMethod();

        if (strtoupper($method) == 'GET') {
            $this->paramIn = $this->request->get();
        } else {
            $this->paramIn = $this->request->getPost();
        }
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
        }
        $this->paramIn = filter_param($this->paramIn);
    }



    public function indexAction()
    {

    }


    /**
     * 修改密码
     * 获取临时凭证
     * @return void
     */
    public function passwordTicketAction()
    {
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $sever               = new PasswordServer($this->lang, $this->timezone);
        $result              = $sever->editPasswordGetTicket($paramIn['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 员工提成
     * @return void
     */
    public function incentiveAction()
    {
        if (!isCountry(['PH','TH'])) {
            $returnData = [
                'code' => ErrCode::ERROR,
                'msg'  => $this->getTranslation()->_('This country is not currently supported.'),
                'data' => null,
            ];
            $this->jsonReturn($returnData);
        }

        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->getIncentiveFromBI($this->userinfo['staff_id']);

        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 个人信息
     */
    public function personAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->getPerson($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 员工个人基本信息
     */
    public function staffAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->getStaff($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 修改个人手机号 或者 银行卡信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @throws ValidationException
     */
    public function updatePersonInfobymobileAction()
    {
        /**
         * 修改银行卡的传参如下：
         *  {
         *      "bank_no":"**********",
         *      "verify_code":"960245",
         *      "verify_type":1,
         *      "bank_card_photo":"https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/**********-5a0cb3bbf7534d8bae5810cd9bab43ff.jpg"
         * }
         * 修改手机号传参如下：
         *  {
         *      "mobile":"**********",
         *      "verify_code":"666666",
         *      "verify_type":1
         *  }
         *
         * 注意：修改该逻辑时注意兼容
         */
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        if($this->check_code($paramIn,$paramIn['staff_id'])){
            if (!empty($paramIn['bank_no']) && !empty($paramIn['bank_type'])){
                (new SysServer($this->lang, $this->timezone))->bankNoVerify($paramIn['bank_type'],$paramIn['bank_no']);
            }
            if (isCountry(['TH','MY'])) {
                if (isset($paramIn['bank_no'])) {
                    if (isset($paramIn['bank_card_photo']) && empty($paramIn['bank_card_photo'])) {
                        return $this->jsonReturn([
                            'code' => ErrCode::ERROR,
                            'msg'  => 'bank_card_photo cannot be empty',
                            'data' => null,
                        ]);
                    }
                    $result = (new PersoninfoServer($this->lang, $this->timezone,
                        $this->userinfo))->modifyHrStaffIdentityAnnexUseLock($paramIn);
                }
            }

            if (isset($result) && $result['code'] != 1) {
                // 附件表如果更新失败 则直接返回
                $this->jsonReturn($result);
            }

            //[2]业务处理
            $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updatePersonInfobymobile($paramIn);
            //[3]数据返回
            $this->jsonReturn($returnArr);
        }else{
            $returnData = [
                'code' => ErrCode::ERROR,
                'msg'  => $this->getTranslation()->_('auth_code_error'),
                'data' => null,
            ];
            $this->jsonReturn($returnData);
        }
    }

    /**
     * 泰国不走这了
     * 修改企业手机号
     */
    public function updateCompanyMobileAction() {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updateMobileCompany($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 修改个人手机号码
     * @return void
     * @throws ValidationException
     */
    public function updatePersonMobileAction()
    {
        //[1]入参
        $paramIn     = $this->paramIn;
        $validations = [
            "verify_code" => "Required|StrLenGeLe:1,10|>>>:verify_code error",
            "mobile"      => "Required|StrLenGeLe:1,20|>>>:mobile error",
            "ticket"      => "Required|StrLenGeLe:1,100|>>>:ticket error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $result = (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updatePersonMobileUseLock($paramIn);
        $this->jsonReturn($result);
    }

    /**
     * 修改个人手机号码 验证
     * @return void
     * @throws BusinessException
     */
    public function checkUpdatePersonMobileAction()
    {
        //[1]入参
        $paramIn     = $this->paramIn;
        $validations = [
            "mobile" => "Required|StrLenGeLe:1,20|>>>:mobile error",
        ];
        $this->validateCheck($paramIn, $validations);
        $result = (new PersoninfoServer($this->lang, $this->timezone,
            $this->userinfo))->checkUpdatePersonMobile($this->userinfo['staff_id'], $paramIn['mobile']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }



    /**
     * 修改个人邮箱
     * @throws ValidationException
     */
    public function updatePersonEmailAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "verify_code"    => "Required|Required|StrLenGeLe:1,10|>>>:verify_code error",
            "personal_email" => 'Required|Email|>>>:' . $this->getTranslation()->_('4109'),
            "ticket"         => "Required|Required|StrLenGeLe:1,100|>>>:ticket error",
        ];
        $this->validateCheck($paramIn, $validations);
        $result = (new PersoninfoServer($this->lang, $this->timezone, $this->userinfo))->updatePersonEmailUseLock($paramIn);
        $this->jsonReturn($result);
    }

    /**
     * 修改银行卡信息
     * @throws ValidationException|\ReflectionException
     */
    public function updateBankInfoAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "bank_no"         => "Required|StrLenGeLe:1,40|>>>:bank_no error",
            "bank_type"       => "Required|Int|>>>:bank_type error",
            "ticket"          => "Required|StrLenGeLe:1,100|>>>:ticket error",
        ];
        if (iscountry(['TH', 'MY', 'PH'])) {
            $validations['bank_card_photo'] = 'Required|StrLenGeLe:1,500|>>>:bank_card_photo error';
        }
        if (isCountry('TH')) {
            $validations['ai_audit_state'] = "Required|Int|>>>:ai_audit_state error";
        }
        $this->validateCheck($paramIn, $validations);
        $result = Tools::reBuildCountryInstance(new PersoninfoServer($this->lang, $this->timezone, $this->userinfo),[$this->lang, $this->timezone])->updateBankInfoUseLock($paramIn);
        $this->jsonReturn($result);
    }

    //修改昵称
    public function updateNickNameAction() {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];


        $returnArr = (new PersoninfoServer($this->lang,$this->timezone))->updatePersonInfobymobile($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * by 展示工资信息
     * @throws \ReflectionException
     */
    public function salaryAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $data                = [];
        $server              = new PersoninfoServer($this->lang, $this->timezone);
        $server              = Tools::reBuildCountryInstance($server, [$this->lang, $this->timezone]);
        if (!empty($paramIn['month'])) {
            $data = $server->getSalaryInfoFromHCM($paramIn);
        }
        if (isCountry('MY') && empty($paramIn['month'])) {
            $data['flash_box_url'] = env('h5_endpoint') . CeoMailEnums::FLASH_BOX_URL;
        }
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }

    /**
     * 工资考勤数据
     * @return void
     * @throws \ReflectionException
     */
    public function salaryAttendanceAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $data = [];
        if ($paramIn['salary_cycle']) {
            $server = new PersoninfoServer($this->lang,$this->timezone);
            $data = $server->salaryAttendanceStat($paramIn['staff_id'],$paramIn['salary_cycle'],'have_detail');
        }
        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }
    /**
     * 获取员工信息接口
     */
    public function staffInfoAction(){
        $staff_id = $this->userinfo['staff_id'];

        $server = new StaffServer();
        $info = $server->get_staff($staff_id);
        $this->jsonReturn($info);
    }


    //更改英文名字
    public function update_name_enAction(){
        try{
            $db_migration = (new SettingEnvServer())->getSetVal('db_migration_20220515');
            if($db_migration){
                throw  new ValidationException($this->getTranslation()->_('system_upgraded'));
            }
            //[1]入参
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $model = new PersoninfoServer($this->lang,$this->timezone);
            $model->update_name_en($paramIn);
            $this->jsonReturn(self::checkReturn([]));
        }catch (ValidationException $validationException){
            $this->jsonReturn(self::checkReturn(-3,$validationException->getMessage()));
        }catch (Exception $e){
            $this->wLog("update_name_en Exception: ".$e->getMessage(), $paramIn, 'personinfo');

            $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }


    //修改网点电话接口 h5 调用 java 写 fle 数据库
    public function update_store_phoneAction(){
        try{
            $param = $this->paramIn;
            //验证是否有权限修改
            $server = new StaffServer();
            $staff_info = $server->get_staff($this->userinfo['id']);

            if(empty($staff_info['data']) || $staff_info['data']['store_manager_id'] != $this->userinfo['id'])
                $this->jsonReturn(self::checkReturn(-3, 'have no permission'));

            if($this->userinfo['organization_type'] != 1)
                $this->jsonReturn(self::checkReturn(-3, 'Can not find any branch'));
            //验证
            if(!empty($param)){
                $post_data = array();
                foreach ($param as $k => $p){
                    $type = trim($p['phone_type']);
                    $number = trim($p['number']);

                    if(empty($number))
                        continue;

                    if(empty($type))
                        $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));

                    if(!in_array($type,array('phone','difficult_phone_second','difficult_phone_first')))
                        $this->jsonReturn(self::checkReturn(-3, 'wrong parameter'));

                    if(empty($number))
                        $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));

                    $post_data[$type] = $number;
                }
            }

            $post_data['staff_info_id'] = $this->userinfo['id'];
            $post_data['store_id'] = $this->userinfo['organization_id'];
            $url = env('api_svc_url').'.SysStoreSvc';
            $method = 'sysStorePhoneUpdate';

            //java 接口 用 zh-CN 客户端 有可能传 zh 那边不好使
            if($this->lang == 'zh')
                $this->lang = 'zh-CN';
            $this->wLog("update_store_phone: {$method}", $post_data, 'personinfo');
            $fle_rpc = (new ApiClient($url,'',$method, $this->lang));
            $fle_rpc->setParams($post_data);
            $res = $fle_rpc->execute();
            $this->wLog("update_store_phone: {$method}", $res, 'personinfo');
            if(empty($res['result']) && !empty($res['error']))
                return $this->jsonReturn(self::checkReturn(-3,$res['error']));

            return $this->jsonReturn(self::checkReturn([]));
        }catch (\Exception $e){
            $this->wLog("update_store_phone:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    //薪资数据 验证密码接口
    public function valid_password_h5Action(){
        try{
            $staff_id = $this->userinfo['id'];
            $re = new StaffRepository($this->lang);
            $info = $re->login_info($staff_id);
            if(empty($info))
                $this->jsonReturn(array('code' => -1,'message' => '','data' => ''));
            $pass_word = $this->paramIn['password'];
            $hash = new PasswordHash(10, false);
            $bool = $hash->checkPassword($pass_word, $info['encrypted_password']);
            if($bool)
                return $this->jsonReturn(self::checkReturn([]));
            else
                return $this->jsonReturn(self::checkReturn(-3,$this->getTranslation()->_('wrong_pwd')));
        }catch (\Exception $e){
            $this->wLog("update_store_phone:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }

    }


    //保存 客户端硬件 手机设备吗 由于获取时间较长 无法登陆时候就获取到
    public function device_tokenAction(){
        $staff_id = $this->userinfo['id'];
        $token = $this->paramIn['device_token'];
        $channel = $this->paramIn['download_channel'];
        $server = new LoginServer($this->lang,$this->timezone);
        $flag = $server->device_token_up($staff_id,$token,$channel);

        $this->jsonReturn(array('code' => intval($flag),'message' => '','data' => ''));
    }


    //退出
    public function logoutAction(){
        //销毁session
        $key = $this->getDI()['config']['application']['authenticate'];
        $token = $this->x_fle_session_id;
        $arr = explode('_', $token);
        $time     = $arr[0];
        $staff_id = $arr[2];
        $auth     = sha1(md5($time . $key . $staff_id));

        $cache_key = md5($auth);
        $flag = $this->getDI()->get('redis')->delete($cache_key);

        $this->jsonReturn(array('code' => intval($flag),'message' => '','data' => ''));
    }

    //薪资数据 验证密码接口
    public function valid_passwordAction(){
        $staff_id = $this->userinfo['id'];
        $re = new StaffRepository($this->lang);
        $info = $re->login_info($staff_id);
        if(empty($info))
            $this->jsonReturn(array('code' => -1,'message' => '','data' => ''));
        $pass_word = $this->paramIn['password'];
        $hash = new PasswordHash(10, false);
        $bool = $hash->checkPassword($pass_word, $info['encrypted_password']);
        $this->jsonReturn(array('code' => intval($bool),'message' => '','data' => ''));
    }



    /**
     * 修改个人邮箱
     * 发送邮件验证码接口
     * @return null
     * @throws ValidationException
     */
    public function send_mail_codeAction()
    {
        $server            = new PersoninfoServer($this->lang, $this->timezone);
        $param['e_mail']   = $this->paramIn['e_mail'];
        $this->validateCheck($param, ['e_mail' => 'Required|Email|>>>:' . $this->getTranslation()->_('4109'),]);

        $param['staff_id'] = $this->userinfo['id'];
        $result               = $server->send_mail($param);
        return $this->jsonReturn($result);
    }



    /**
     * 验证 手机验证码或者邮箱验证码·
     * @Deprecated
     * @param $paramIn
     * @param $staff_id
     * @return bool
     */
    protected function check_code($paramIn,$staff_id){
        $verify_code = $paramIn['verify_code'];
        $cache_key = ($paramIn['verify_type'] == 1) ? 'CODE_'.$staff_id : 'MAIL_CODE_' . $staff_id;
        //从缓存中读取
        $cache = $this->getDI()->get('redis');
        $cache_code = $cache->get($cache_key);
        if($verify_code == $cache_code || (get_runtime() == 'dev' && $verify_code == '666666')){
            return true;
        }
        return false;

    }

    //客户端接口 返回值 是 message 同时返回 最新版本号 因为没有合适的接口放版本号 这个接口跟设备相关
    //"field_punch": false, 这两个字段 应该是准备从 登陆接口迁移过来
    //"wage_display": true
    public function device_infoAction()
    {
        /**
         * `device_model` varchar(200) DEFAULT NULL COMMENT '设备型号',
         * `equipment_type` varchar(60) DEFAULT NULL COMMENT '服务端 seed 1-kit 2-bs 3-backyard',
         * `current_ip` varchar(20) DEFAULT NULL COMMENT '本次登录ip',
         * `network_type` varchar(64) DEFAULT NULL COMMENT '运营商类型或者wifi',
         * `lat` decimal(11,8) DEFAULT NULL COMMENT '位置的纬度',
         * `lng` decimal(11,8) DEFAULT NULL COMMENT '位置经度',
         * `current_time` datetime DEFAULT NULL COMMENT '本次登录时间',
         * `version` varchar(20) DEFAULT NULL COMMENT '客户端版本',
         * `os` varchar(30) DEFAULT NULL COMMENT '本次登录os',
         * `client_id` varchar(128) DEFAULT NULL COMMENT '标识app，若同一个发布账号下所以app都被删除，重新安装此值会改变。',
         * `device_id` varchar(200) DEFAULT NULL COMMENT '广告标识符，可作为设备唯一标识，但有可能获取不到',
         */

        try {
            //获取版本号
            $headerData     = $this->request->getHeaders();
            $equipment_type = $this->processingDefault($headerData, 'X-Fle-Equipment-Type');
//
            $client_version          = $this->paramIn['version'];
            $equipment_type          = strtoupper($equipment_type);
            $enum_type               = \FlashExpress\bi\App\library\enums::EQM_TYPE;
            $param['equipment_type'] = empty($enum_type[$equipment_type]) ? 0 : $enum_type[$equipment_type];
            $param['os']             = $this->paramIn['os'];//ios  android
            $server                  = new PersoninfoServer($this->lang, $this->timezone);
            //获取版本号 不try catch 保证业务处理失败也要返回版本号
            $info = $server->get_versionFromCache($param['equipment_type'], $param['os'], $client_version,
                $this->userinfo);                           //array('version' => '1.2.2')


            //考勤白名单相关的字段  从登陆接口拆分出来的 "field_punch" wage_display": // --todo
            $re = new StaffRepository($this->lang);
            //网点员工  并且 (StaffInfoFormal.INFORMAL.equals(staffInfo.getFormal()) || StoreCategory.FRANCHISEE_SHOP.equals(sysStore.getCategory())) {
            //是否显示薪资 java登陆迁移字段
            $info['wage_display'] = false;
            //是否可 外勤打卡
            $info['field_punch']    = false;
            $param['staff_info_id'] = $this->userinfo['id'];
            $param['device_model']  = $this->paramIn['device_model'];
            $param['current_ip']    = isset($this->paramIn['current_ip']) && $this->paramIn['current_ip'] ? $this->paramIn['current_ip']: '';
            $param['network_type']  = isset($this->paramIn['network_type']) && $this->paramIn['network_type'] ? $this->paramIn['network_type']: '';
            $param['lat']           = $this->paramIn['lat'];
            $param['lng']           = $this->paramIn['lng'];
            $param['version']       = $this->paramIn['version'];
            $param['os']            = strtolower($this->paramIn['os']);
            $param['client_id']     = $this->paramIn['client_id'];
            $param['device_id']     = $this->paramIn['device_id'];


//            $flag = $server->save_device($param);
            $this->logger->write_log("upload_device_info_{$this->userinfo['id']} " . json_encode($param) . json_encode($info),
                'info');

//            $cache->save($cache_key, json_encode($info),3600);

            $this->jsonReturn(['code' => 1, 'message' => '', 'data' => $info]);
            //带url链接 可能要用这个
            //die(json_encode(array('code' => 1,'message' => '','data' => $info),JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES+JSON_PRETTY_PRINT));
        } catch (\Exception $e) {
            $this->logger->write_log("upload_device_info_{$this->userinfo['id']} " . $e->getMessage() . json_encode($param) . json_encode($info),
                'info');
            $this->jsonReturn(['code' => 1, 'message' => '', 'data' => $info]);
        }
    }

    /**
     * ai 身份证照片认证
     */
    public function ai_id_card_auditAction() {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $model = new PersoninfoServer($this->lang,$this->timezone);
            $return = $model->ai_id_card_audit($paramIn);

            return $this->jsonReturn($return);
        } catch (\Exception $e) {
            $this->wLog("ai_id_card_auditAction:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    /**
     * ai 身份证审核提交
     */
    public function ai_id_card_submitAction() {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $redis_lock_key  = "lock_ai_id_card_submit_" . $paramIn['staff_id']; //锁 key
            $return = $this->atomicLock(function () use ($paramIn) {
                return  (new PersoninfoServer($this->lang,$this->timezone))->ai_id_card_submit($paramIn);
            }, $redis_lock_key, 10);
            if ($return === false) { //没有获取到锁
                return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('ticket_repeat_msg')));
            }
            return $this->jsonReturn($return);
        } catch (\Exception $e) {

            $this->wLog("ai_id_card_submitAction:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }

    /**
     * 获取工号身份证附件信息
     * 废弃了 前端没有用
     */
    public function staff_identity_annexAction() {
        try {
            //[1]入参
            $paramIn = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];

            $model = new PersoninfoServer($this->lang,$this->timezone);
            $return = $model->get_staff_identity_annex($paramIn);

            return $this->jsonReturn(self::checkReturn(['data' => $return]));
        } catch (\Exception $e) {
            $this->wLog("ai_id_card_submitAction:", $e->getMessage(), 'personinfo');
            return $this->jsonReturn(self::checkReturn(-3,'server error'));
        }
    }



    /**
     * @description:修改个人信息
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/9/1 20:20
     */
    public function  updateStaffInfoAction(){
        //[1]入参
        $paramIn = [];
        if(isset($this->paramIn['tax_card'])){
            $paramIn['tax_card'] = $this->paramIn['tax_card'];
        }
        if(isset($this->paramIn['bank_branch_name'])){
            $paramIn['bank_branch_name'] = $this->paramIn['bank_branch_name'];
        }
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations = [
            'tax_card'=>'StrLenLe:100|>>>:'.'tax card fill in unqualified !',
            'bank_branch_name'=>'StrLenLe:100|>>>:'.'bank branch name fill in unqualified !',
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr =  (new PersoninfoServer($this->lang,$this->timezone,$this->userinfo))->updatePersonInfobymobileUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取 invoice 信息
     * @throws ValidationException
     */
    public function getInvoiceInfoAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $returnArr = (new PersoninfoServer($this->lang, $this->timezone))->getInvoiceInfoFromFbi($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 获取 付款凭证或代扣税
     * type：1 付款凭证，2 代扣税
     *
     * @throws ValidationException
     */
    public function getVoucherInfoAction()
    {
        $paramIn = $this->paramIn;
        //[1]入参
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        $validations = [
            "type" => "Required|IntIn:1,2", // 1 付款凭证，2 代扣税
        ];
        $this->validateCheck($paramIn, $validations);

        $returnArr = (new PersoninfoServer($this->lang, $this->timezone))->getVoucherFromFbi($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }


    /**
     * 工资下拉月份
     */
    public function salary_type_listAction(){
        $staff_id = $this->userinfo['staff_id'];
        $ac = new ApiClient('hcm_rpc', '', 'get_salary_type_list' ,$this->lang);
        $ac->setParams([
            "staff_id" => $staff_id,
        ]);
        $returnData = $ac->execute();
        if($returnData["result"]['code'] == 1) {
            $this->jsonReturn(self::checkReturn(['data'=>$returnData["result"]['data']]));
        } else {
            $this->logger->write_log("personal salary get failed {$staff_id}");
            $this->jsonReturn(self::checkReturn(-3,'from hcm for salary data get failed'));
        }
    }

    /**
     * 获取 付款凭证,代扣税 Invoice 周期枚举
     *
     *
     * @throws ValidationException
     */
    public function getPeriodAction()
    {
        //[1]入参
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $returnArr = (new PersoninfoServer($this->lang, $this->timezone))->getPeriodFromFbi($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 我的 个人信息 基本信息--上传户口簿
     */
    public function residenceBookletSaveAction()
    {
        $paramIn = $this->paramIn;
        //[1]入参
        $paramIn['staff_info_id'] = $this->userinfo['staff_id'];

        $validations = [
            "residence_booklet_file_url_first"  => "Required|StrLenGe:0|>>>:residence_booklet_file_url_first error", // 户口簿第一页
            "residence_booklet_file_url_second" => "Required|StrLenGe:0|>>>:residence_booklet_file_url_second error", // 户口簿本人信息页
        ];
        $this->validateCheck($paramIn, $validations);

        $returnArr = (new PersoninfoServer($this->lang, $this->timezone))->residenceBookletSaveUseLock($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn(['msg'=>$this->getTranslation()->_('5002'), 'data' => $returnArr]));
    }

    /**
     * 获取员工企业号码字段信息
     * @return void
     */
    public function companyMobileInfoAction()
    {
        $result = (new CompanyMobileServer($this->lang, $this->timezone))->mobileInfo($this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 企业号码是否可编辑
     * @api https://yapi.flashexpress.pub/project/93/interface/api/88295
     * @throws Exception
     */
    public function companyMobileEditAction()
    {
        $result = (new CompanyMobileServer($this->lang, $this->timezone))->checkCompanyMobileCanEdit($this->userinfo);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 获可选的企业号码
     * @api https://yapi.flashexpress.pub/project/93/interface/api/86375
     * @return void
     */
    public function getCompanyMobileListAction()
    {
        $params      = $this->paramIn;
        $validations = [
            'sim_card_number' => 'Required|StrLenGeLe:13,18', // SIM序号
        ];
        $this->validateCheck($params, $validations);
        $result = (new CompanyMobileServer($this->lang, $this->timezone))->getCompanyMobileList($params['sim_card_number'], $this->userinfo['staff_id']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 保存企业号码
     * @return void
     * @throws ValidationException
     */
    public function saveCompanyMobileAction()
    {
        $params     = $this->paramIn;
        $validations = [
            'phone_number'    => 'Required|StrLen:10',
            'sim_card_number' => 'Required|StrLenGeLe:13,18', // SIM序号
        ];
        $this->validateCheck($params, $validations);
        $result = (new CompanyMobileServer($this->lang,
            $this->timezone))->saveUseLock($this->userinfo['staff_id'],$params['phone_number'],$params['sim_card_number']);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }

    /**
     * 企业号码异常反馈
     * @return void
     */
    public function companyMobileFeedbackAction()
    {
        $params = $this->paramIn;
        $this->validateCheck([
            'category' => $params['category'] ?? '',
            'reason'   => $params['reason'] ?? '',
        ], [
            'category' => 'Required|Int|>>>:category error',
            'reason'   => 'Required|StrLenGeLe:1,1000|>>>:reason error',
        ]);
        $result = (new CompanyMobileServer($this->lang,$this->timezone))->feedBackUseLock($this->userinfo['staff_id'],$params);
        $this->jsonReturn($this->checkReturn(['data' => $result]));
    }
}
