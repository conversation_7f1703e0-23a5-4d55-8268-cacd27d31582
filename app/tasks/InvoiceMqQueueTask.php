<?php

use FlashExpress\bi\App\library\RocketMQ;

/**
 * Author: Bruce
 * Date  : 2024-01-24 22:04
 * Description:
 */

use FlashExpress\bi\App\Server\InvoiceServer;


class InvoiceMqQueueTask extends RocketMqBaseTask
{
    public function initialize()
    {
        $this->tq = "send_invoice";//发送invoice
        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 个人代理 Invoice 消费
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        try {
            $this->getDI()->get('logger')->write_log('InvoiceMqQueueTask processOneMsg ' . $msgBody,
                'info');
            $msgBody     = base64_decode($msgBody);
            $messageBody = json_decode($msgBody, true);
            if (empty($messageBody)) {
                return false;
            }

            if (!empty($messageBody['data']) && !empty($messageBody['data']['data']) && !empty($messageBody['data']['type']) && $messageBody['data']['type'] == RocketMQ::TAG_NAME_SEND_INVOICE) {
                return InvoiceServer::getInstance($this->lang, $this->timezone)->handle($messageBody['data']['data']);
            }
            return false;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('InvoiceMqQueueTask mq_num_sub 异常 ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送异常，补发 消息
     * @param $params
     * @return bool
     */
    public function send_messageAction($params)
    {
        if (empty($params[0])) {
            echo "请传 消息参数 样例: '{\"staff_info_id\":\"118725\",\"start_date\":\"01\/01\/2024\",\"end_date\":\"31\/01\/2024\"}'";
            return false;
        }

        $res = InvoiceServer::getInstance($this->lang, $this->timezone)->sendMessage($params[0]);
        if ($res) {
            echo "success";
            return true;
        }
        echo "fail";
        return false;
    }

    /**
     * 发送异常，补发 邮件
     * @param $params
     * @return bool
     */
    public function send_emailAction($params)
    {
        if (empty($params[0])) {
            echo "请传 消息参数 样例: '{\"staff_info_id\":\"118725\",\"name\":\"CESSADAS\",\"email\":\"<EMAIL>\",\"start_date\":\"01\/01\/2024\",\"end_date\":\"31\/01\/2024\",\"link\":\"https:\/\/dev-01-my-backyard-ui.fex.pub\/#\/LinkLogin?source=invoice&start_date=01\/01\/2024&end_data=31\/01\/2024&staff_info_id=118725\"}'";
            return false;
        }

        $res = InvoiceServer::getInstance($this->lang, $this->timezone)->sendEmail($params[0]);
        if ($res) {
            echo "success";
            return true;
        }
        echo "fail";
        return false;
    }


    public function testAction()
    {
        $msgBody     = '{"data":{"type":"SEND_INVOICE","data":{"data_id":"8776","period_id":"202505213","staff_info_id":"2000005","staff_name":"\u0e19\u0e32\u0e22 test 2000005","store_id":"MY01010103","store_name":"PKS_SP-Pokok Sena SPS_SP-Seberang Perai Selatan_MY","hire_date":"2024-02-07","start_date":"2025-05-16","end_date":"2025-05-31","attend_days":"0.00","ph_attend_days":"0.00","ph_delivery_bonus":"160.00","pickup_amount":"80.00","delivery_amount":"40.00","help_amount":"4.00","floor_subsidy":"42.00","other_addition":14,"productivity_bonus":"9.00","income_total_amount":"407.00","penalty":33,"early_termination_punish":"13.00","early_termination_day_punish":"14.00","asset_deduction_money":"15.00","held_money":"16.00","other_deduction":75.53999999999999,"total_amount":"240.46","job_title":"13","vehicle_category":"8","normal_pvd_amount":"10.00","add_pvd_amount":"30.00","car_subsidy":"5.00","gdl_subsidy":"6.00","referral_subsidy":"7.00"}}}';
        $messageBody = json_decode($msgBody, true);
        InvoiceServer::getInstance($this->lang, $this->timezone)->handle($messageBody['data']['data']);
    }

    /**
     * invoice 重新发送邮件
     * @return bool
     */
    public function resend_emailAction()
    {
        $res = InvoiceServer::getInstance($this->lang, $this->timezone)->resendEmail();
        if ($res) {
            echo "success";
            return true;
        }
        echo "fail";
        return false;
    }

}